flutter run -d linux
Launching lib/main.dart on Linux in debug mode...
Building Linux application...                                           
✓ Built build/linux/x64/debug/bundle/signage
Monitor 0: 1920x1080
Total dimensions: 1920x1080
flutter: Detected 1 monitors:
flutter: Monitor 0: (0.0, 0.0) 1920.0x1080.0, isPrimary: true
flutter: Total dimensions: 1920.0 x 1080.0
Syncing files to device Linux...                                   276ms

Flutter run key commands.
r Hot reload. 🔥🔥🔥
R Hot restart.
h List all available interactive commands.
d Detach (terminate "flutter run" but leave application running).
c Clear the screen
q Quit (terminate the application on the device).

A Dart VM Service on Linux is available at: http://127.0.0.1:46851/m3uuOOVmsxU=/
flutter: supabase.supabase_flutter: INFO: ***** Supabase init completed ***** 
flutter: [SupabaseService] Supabase initialized with realtime enabled
flutter: Created signage directory structure at: /home/<USER>/Documents/signage
flutter: Content directory: /home/<USER>/Documents/signage/content
flutter: Data directory: /home/<USER>/Documents/signage/data
flutter: Temp directory: /home/<USER>/Documents/signage/temp
The Flutter DevTools debugger and profiler on Linux is available at: http://127.0.0.1:9101?uri=http://127.0.0.1:46851/m3uuOOVmsxU=/
flutter: Starting player controller service
flutter: PlayerControllerService: Starting background service
flutter: Starting logging service
flutter: LoggingService: Starting
flutter: PlayerScheduleService: Parsed start time: 08:00:00
flutter: PlayerScheduleService: Parsed end time: 20:00:00
flutter: PlayerScheduleService: Current time: 15:56:40
flutter: PlayerScheduleService: Start time: 08:00:00
flutter: PlayerScheduleService: End time: 20:00:00
flutter: PlayerScheduleService: Current time is between start and end time - starting content display
flutter: PlayerScheduleService: Starting content display
flutter: PlayerScheduleService: Start content callback received
flutter: PlayerScheduleService: Setting stop timer for 14599 seconds (4h 3m 19s)
flutter: PlayerScheduleService: Service started
flutter: PlayerControllerService: Loaded settings for screen c26fd436-f692-4bc1-853d-ee9ee478b94f
flutter: PlayerControllerService: Update frequency: 00:00:30
flutter: PlayerControllerService: Setting update timer to 30 seconds
flutter: PlayerControllerService: Subscribing to realtime updates for screen c26fd436-f692-4bc1-853d-ee9ee478b94f
flutter: PlayerControllerService: Using channel name: public:screens:c26fd436-f692-4bc1-853d-ee9ee478b94f
flutter: PlayerControllerService: Testing realtime connection
flutter: PlayerControllerService: Subscription is active
flutter: PlayerControllerService: Channel name: public:screens:c26fd436-f692-4bc1-853d-ee9ee478b94f
flutter: PlayerControllerService: Listening for updates to screen c26fd436-f692-4bc1-853d-ee9ee478b94f
flutter: LoggingService: Loaded settings for screen c26fd436-f692-4bc1-853d-ee9ee478b94f
flutter: LoggingService: Loaded screen ID: c26fd436-f692-4bc1-853d-ee9ee478b94f
flutter: LoggingService: Logging player start
flutter: LoggingService: Logged player start
flutter: LoggingService: Processing queue - Activities: 1, Proof of Play: 0
flutter: LoggingService: Processing 1 activity logs
flutter: LoggingService: Sending activity log to Supabase: Player Start
flutter: [SupabaseService] Logging screen activity: Player Start
flutter: [SupabaseService] Activity data: {screen_id: c26fd436-f692-4bc1-853d-ee9ee478b94f, log_details: Player Start, log_datetime: 2025-05-22T10:26:40.460192Z}
flutter: LoggingService: Queue is already being processed, skipping
flutter: Loaded state: campaign=0, schedule=4
flutter: Playing schedule item: promo1.jpg (type: SimpleMedia)
flutter: No current widget (first content or null widget)
flutter: ====================================================
flutter: Creating SimpleMediaWidget for promo1.jpg
flutter: ID: b81b47c5-8469-4ae8-9980-d273a53bccc9
flutter: File Path: /home/<USER>/Documents/signage/content/promo1.jpg
flutter: Is Image: true, Is Video: false
flutter: ====================================================
flutter: Storing content widget for schedule item: b81b47c5-8469-4ae8-9980-d273a53bccc9
flutter: LoggingService: Logged simple media display: b81b47c5-8469-4ae8-9980-d273a53bccc9
flutter: LoggingService: Queue is already being processed, skipping
flutter: Displaying content widget for schedule item: b81b47c5-8469-4ae8-9980-d273a53bccc9
flutter: SimpleMediaWidget: initState for promo1.jpg
flutter: ImageTimerWidget: initState for /home/<USER>/Documents/signage/content/promo1.jpg
flutter: ImageTimerWidget: starting timer for /home/<USER>/Documents/signage/content/promo1.jpg with duration 8 seconds
flutter: ImageTimerWidget: image loaded for /home/<USER>/Documents/signage/content/promo1.jpg
flutter: [SupabaseService] Screen activity log response: [{id: 128, screen_id: c26fd436-f692-4bc1-853d-ee9ee478b94f, log_details: Player Start, log_datetime: 2025-05-22T10:26:40.460192+00:00, file_download_count: null, total_file_download: null}]
flutter: LoggingService: Activity log result: true
flutter: LoggingService: Processing 1 proof of play logs
flutter: LoggingService: Sending proof of play log to Supabase: ProofOfPlay(id: null, screenId: c26fd436-f692-4bc1-853d-ee9ee478b94f, campaignId: fe2fddc8-f709-4b54-b6c9-c95bf1ed9852, slideId: null, mediaId: b81b47c5-8469-4ae8-9980-d273a53bccc9, logDatetime: 2025-05-22 10:26:40.739245Z)
flutter: [SupabaseService] Logging proof of play: ProofOfPlay(id: null, screenId: c26fd436-f692-4bc1-853d-ee9ee478b94f, campaignId: fe2fddc8-f709-4b54-b6c9-c95bf1ed9852, slideId: null, mediaId: b81b47c5-8469-4ae8-9980-d273a53bccc9, logDatetime: 2025-05-22 10:26:40.739245Z)
flutter: [SupabaseService] Proof of play data: {screen_id: c26fd436-f692-4bc1-853d-ee9ee478b94f, log_datetime: 2025-05-22T10:26:40.739245Z, campaign_id: fe2fddc8-f709-4b54-b6c9-c95bf1ed9852, slide_id: null, media_id: b81b47c5-8469-4ae8-9980-d273a53bccc9}
flutter: [SupabaseService] Proof of play log response: [{id: 586, screen_id: c26fd436-f692-4bc1-853d-ee9ee478b94f, campaign_id: fe2fddc8-f709-4b54-b6c9-c95bf1ed9852, slide_id: null, media_id: b81b47c5-8469-4ae8-9980-d273a53bccc9, log_datetime: 2025-05-22T10:26:40.739245+00:00}]
flutter: LoggingService: Proof of play log result: true
flutter: ImageTimerWidget: timer completed for /home/<USER>/Documents/signage/content/promo1.jpg
flutter: ====================================================
flutter: Content completed, moving to next item
flutter: Completed widget details:
flutter: Name: promo1.jpg
flutter: ID: b81b47c5-8469-4ae8-9980-d273a53bccc9
flutter: Type: SimpleMedia
flutter: Media Type: Image
flutter: ====================================================
flutter: Last item in schedule, preparing to loop back to first item
flutter: Resetting from schedule item 4 to 0
flutter: First schedule item: BetterWithPepsi.mp4
flutter: ====================================================
flutter: Playing first schedule item: BetterWithPepsi.mp4
flutter: ID: 5c1fa150-ec88-4b65-8693-003c728939e5
flutter: (Looping back to first item)
flutter: ====================================================
flutter: ====================================================
flutter: Starting transition to first content: BetterWithPepsi.mp4
flutter: Current content: b81b47c5-8469-4ae8-9980-d273a53bccc9
flutter: Next content: 5c1fa150-ec88-4b65-8693-003c728939e5
flutter: ====================================================
flutter: ====================================================
flutter: Current widget that has finished playing:
flutter: Name: BetterWithPepsi.mp4
flutter: ID: 5c1fa150-ec88-4b65-8693-003c728939e5
flutter: Type: SimpleMedia
flutter: ====================================================
flutter: Saved state: campaign=0, schedule=0
flutter: ====================================================
flutter: Creating SimpleMediaWidget for BetterWithPepsi.mp4
flutter: ID: 5c1fa150-ec88-4b65-8693-003c728939e5
flutter: File Path: /home/<USER>/Documents/signage/content/BetterWithPepsi.mp4
flutter: Is Image: false, Is Video: true
flutter: ====================================================
flutter: Storing content widget for schedule item: 5c1fa150-ec88-4b65-8693-003c728939e5
flutter: LoggingService: Logged simple media display: 5c1fa150-ec88-4b65-8693-003c728939e5
flutter: LoggingService: Processing queue - Activities: 0, Proof of Play: 1
flutter: LoggingService: Processing 1 proof of play logs
flutter: LoggingService: Sending proof of play log to Supabase: ProofOfPlay(id: null, screenId: c26fd436-f692-4bc1-853d-ee9ee478b94f, campaignId: fe2fddc8-f709-4b54-b6c9-c95bf1ed9852, slideId: null, mediaId: 5c1fa150-ec88-4b65-8693-003c728939e5, logDatetime: 2025-05-22 10:26:48.844914Z)
flutter: [SupabaseService] Logging proof of play: ProofOfPlay(id: null, screenId: c26fd436-f692-4bc1-853d-ee9ee478b94f, campaignId: fe2fddc8-f709-4b54-b6c9-c95bf1ed9852, slideId: null, mediaId: 5c1fa150-ec88-4b65-8693-003c728939e5, logDatetime: 2025-05-22 10:26:48.844914Z)
flutter: [SupabaseService] Proof of play data: {screen_id: c26fd436-f692-4bc1-853d-ee9ee478b94f, log_datetime: 2025-05-22T10:26:48.844914Z, campaign_id: fe2fddc8-f709-4b54-b6c9-c95bf1ed9852, slide_id: null, media_id: 5c1fa150-ec88-4b65-8693-003c728939e5}
flutter: Starting transition animation
flutter: Displaying content widget for schedule item: 5c1fa150-ec88-4b65-8693-003c728939e5
flutter: SimpleMediaWidget: initState for BetterWithPepsi.mp4
flutter: ImageTimerWidget: image loaded for /home/<USER>/Documents/signage/content/promo1.jpg
flutter: SimpleMediaWidget: error playing video: UnimplementedError: init() has not been implemented.
flutter: ====================================================
flutter: Content completed, moving to next item
flutter: Completed widget details:
flutter: Name: BetterWithPepsi.mp4
flutter: ID: 5c1fa150-ec88-4b65-8693-003c728939e5
flutter: Type: SimpleMedia
flutter: Media Type: Video
flutter: ====================================================
flutter: Moving from schedule item 0 to 1
flutter: New schedule item: FBN2407-VRefresh-BlackcurrantYuZu-1920x540-v1.mp4
flutter: ====================================================
flutter: Playing next schedule item: FBN2407-VRefresh-BlackcurrantYuZu-1920x540-v1.mp4
flutter: ID: 104a97fe-2928-4a35-b785-fbdc83765845
flutter: ====================================================
flutter: ====================================================
flutter: Starting transition to next content: FBN2407-VRefresh-BlackcurrantYuZu-1920x540-v1.mp4
flutter: Current content: 5c1fa150-ec88-4b65-8693-003c728939e5
flutter: Next content: 104a97fe-2928-4a35-b785-fbdc83765845
flutter: ====================================================
flutter: ====================================================
flutter: Current widget that has finished playing:
flutter: Name: FBN2407-VRefresh-BlackcurrantYuZu-1920x540-v1.mp4
flutter: ID: 104a97fe-2928-4a35-b785-fbdc83765845
flutter: Type: SimpleMedia
flutter: ====================================================
flutter: Saved state: campaign=0, schedule=1
flutter: ====================================================
flutter: Creating SimpleMediaWidget for FBN2407-VRefresh-BlackcurrantYuZu-1920x540-v1.mp4
flutter: ID: 104a97fe-2928-4a35-b785-fbdc83765845
flutter: File Path: /home/<USER>/Documents/signage/content/FBN2407-VRefresh-BlackcurrantYuZu-1920x540-v1.mp4
flutter: Is Image: false, Is Video: true
flutter: ====================================================
flutter: Storing content widget for schedule item: 104a97fe-2928-4a35-b785-fbdc83765845
flutter: LoggingService: Logged simple media display: 104a97fe-2928-4a35-b785-fbdc83765845
flutter: LoggingService: Queue is already being processed, skipping
flutter: Starting transition animation
flutter: Displaying content widget for schedule item: 104a97fe-2928-4a35-b785-fbdc83765845
flutter: SimpleMediaWidget: initState for FBN2407-VRefresh-BlackcurrantYuZu-1920x540-v1.mp4
flutter: ====================================================
flutter: Disposing video player for /home/<USER>/Documents/signage/content/BetterWithPepsi.mp4
flutter: ====================================================
flutter: SimpleMediaWidget: dispose for BetterWithPepsi.mp4
flutter: SimpleMediaWidget: error playing video: UnimplementedError: init() has not been implemented.
flutter: ====================================================
flutter: Content completed, moving to next item
flutter: Completed widget details:
flutter: Name: FBN2407-VRefresh-BlackcurrantYuZu-1920x540-v1.mp4
flutter: ID: 104a97fe-2928-4a35-b785-fbdc83765845
flutter: Type: SimpleMedia
flutter: Media Type: Video
flutter: ====================================================
flutter: Moving from schedule item 1 to 2
flutter: New schedule item: Fire Index
flutter: ====================================================
flutter: Playing next schedule item: Fire Index
flutter: ID: b13177ab-b613-46fc-9b8e-7eec420c53b9
flutter: ====================================================
flutter: ====================================================
flutter: Starting transition to next content: Fire Index
flutter: Current content: 104a97fe-2928-4a35-b785-fbdc83765845
flutter: Next content: b13177ab-b613-46fc-9b8e-7eec420c53b9
flutter: ====================================================
flutter: ====================================================
flutter: Current widget that has finished playing:
flutter: Name: Fire Index
flutter: ID: b13177ab-b613-46fc-9b8e-7eec420c53b9
flutter: Type: SlideShow
flutter: ====================================================
flutter: ====================================================
flutter: Creating SlideShowWidget for Fire Index
flutter: ID: b13177ab-b613-46fc-9b8e-7eec420c53b9
flutter: ====================================================
flutter: Storing content widget for schedule item: b13177ab-b613-46fc-9b8e-7eec420c53b9
flutter: LoggingService: Logged slide display: b13177ab-b613-46fc-9b8e-7eec420c53b9
flutter: Starting transition animation
flutter: Saved state: campaign=0, schedule=2
flutter: Displaying content widget for schedule item: b13177ab-b613-46fc-9b8e-7eec420c53b9
flutter: SlideShowWidget: initState for Fire Index
flutter: SlideShowWidget: Building content with design dimensions: 1920.0 x 1080.0
flutter: SlideShowWidget: Actual screen dimensions: 1920.0 x 1080.0
flutter: SlideShowWidget: Original API position: (40.66670735677076, 55.65409342447913), size: 1789.1249593098955 x 851.6791788736978, zIndex: 1
flutter: SlideShowWidget: Creating GLOBAL API data controller for all APIWidgets in this slide
flutter: ApiDataController: Created with apiUrl: https://67e53c0318194932a5852f51.mockapi.io/api/v1/fireindex, apiDataPreviewDuration: 0
flutter: ApiDataController: Fetching data from https://67e53c0318194932a5852f51.mockapi.io/api/v1/fireindex
flutter: ApiDataController: Setting up refresh timer for every 5 minutes
flutter: SlideShowWidget: GLOBAL API data controller created with apiDataPreviewDuration: 0
flutter: SlideShowWidget: Expected scaled API position: (40.66670735677076, 55.65409342447913), size: 1789.1249593098955 x 851.6791788736978
flutter: ====================================================
flutter: Disposing video player for /home/<USER>/Documents/signage/content/FBN2407-VRefresh-BlackcurrantYuZu-1920x540-v1.mp4
flutter: ====================================================
flutter: SimpleMediaWidget: dispose for FBN2407-VRefresh-BlackcurrantYuZu-1920x540-v1.mp4
flutter: [SupabaseService] Proof of play log response: [{id: 587, screen_id: c26fd436-f692-4bc1-853d-ee9ee478b94f, campaign_id: fe2fddc8-f709-4b54-b6c9-c95bf1ed9852, slide_id: null, media_id: 5c1fa150-ec88-4b65-8693-003c728939e5, log_datetime: 2025-05-22T10:26:48.844914+00:00}]
flutter: LoggingService: Proof of play log result: true
flutter: Displaying content widget for schedule item: b13177ab-b613-46fc-9b8e-7eec420c53b9
flutter: SlideShowWidget: Player screen size: 1920.0x1080.0
flutter: SlideShowWidget: Design size: 1920.0 x 1080.0
flutter: SlideShowWidget: Scaling factors: horizontal=1.0, vertical=1.0
flutter: SlideShowWidget: Building scaled widgets with:
flutter:   - Design dimensions: 1920.0 x 1080.0
flutter:   - Screen dimensions: 1920.0 x 1080.0
flutter:   - Scale factors: 1.0 x 1.0
flutter: SlideShowWidget: Scaling widget:
flutter:   - Original: left=40.66670735677076, top=55.65409342447913, width=1789.1249593098955, height=851.6791788736978
flutter:   - Scaled: left=40.66670735677076, top=55.65409342447913, width=1789.1249593098955, height=851.6791788736978
flutter: APIWidget: initState for e53e3897-8ef3-4b92-b1f8-962c2737074b
flutter: APIWidget: Building widget with state - isLoading: true, hasError: false, isInitialized: false, contentWidgets: 0
flutter: APIWidget: Dimensions - width: 1789.1249593098955, height: 851.6791788736978
flutter: APIWidget: Showing loading state
flutter: ====================================================
flutter: Completing transition
flutter: Current content ID: b13177ab-b613-46fc-9b8e-7eec420c53b9
flutter: Next content ID: b13177ab-b613-46fc-9b8e-7eec420c53b9
flutter: ====================================================
flutter: Displaying content widget for schedule item: b13177ab-b613-46fc-9b8e-7eec420c53b9
flutter: SlideShowWidget: Player screen size: 1920.0x1080.0
flutter: SlideShowWidget: Design size: 1920.0 x 1080.0
flutter: SlideShowWidget: Scaling factors: horizontal=1.0, vertical=1.0
flutter: SlideShowWidget: Building scaled widgets with:
flutter:   - Design dimensions: 1920.0 x 1080.0
flutter:   - Screen dimensions: 1920.0 x 1080.0
flutter:   - Scale factors: 1.0 x 1.0
flutter: SlideShowWidget: Scaling widget:
flutter:   - Original: left=40.66670735677076, top=55.65409342447913, width=1789.1249593098955, height=851.6791788736978
flutter:   - Scaled: left=40.66670735677076, top=55.65409342447913, width=1789.1249593098955, height=851.6791788736978
flutter: APIWidget: Building widget with state - isLoading: true, hasError: false, isInitialized: false, contentWidgets: 0
flutter: APIWidget: Dimensions - width: 1789.1249593098955, height: 851.6791788736978
flutter: APIWidget: Showing loading state
flutter: ImageTimerWidget: dispose for /home/<USER>/Documents/signage/content/promo1.jpg
flutter: ImageTimerWidget: canceling timer for /home/<USER>/Documents/signage/content/promo1.jpg
flutter: SimpleMediaWidget: dispose for promo1.jpg
flutter: ====================================================
flutter: Completing transition
flutter: Current content ID: b13177ab-b613-46fc-9b8e-7eec420c53b9
flutter: Next content ID: null
flutter: ====================================================
flutter: WARNING: No next content widget or ID available for transition
flutter: Attempting to recover by moving to the next item
flutter: Moving from schedule item 2 to 3
flutter: New schedule item: nzdeliveryapp_digi-eyeline-1920x540.png
flutter: ====================================================
flutter: Current widget that has finished playing:
flutter: Name: nzdeliveryapp_digi-eyeline-1920x540.png
flutter: ID: 75e20d80-47ed-4274-aca9-0251f2a182b5
flutter: Type: SimpleMedia
flutter: ====================================================
flutter: Saved state: campaign=0, schedule=3
flutter: Displaying content widget for schedule item: b13177ab-b613-46fc-9b8e-7eec420c53b9
flutter: ====================================================
flutter: Completing transition
flutter: Current content ID: b13177ab-b613-46fc-9b8e-7eec420c53b9
flutter: Next content ID: null
flutter: ====================================================
flutter: WARNING: No next content widget or ID available for transition
flutter: Attempting to recover by moving to the next item
flutter: Moving from schedule item 3 to 4
flutter: New schedule item: promo1.jpg
flutter: ====================================================
flutter: Current widget that has finished playing:
flutter: Name: promo1.jpg
flutter: ID: b81b47c5-8469-4ae8-9980-d273a53bccc9
flutter: Type: SimpleMedia
flutter: ====================================================
flutter: Saved state: campaign=0, schedule=4
flutter: ====================================================
flutter: Creating SimpleMediaWidget for nzdeliveryapp_digi-eyeline-1920x540.png
flutter: ID: 75e20d80-47ed-4274-aca9-0251f2a182b5
flutter: File Path: /home/<USER>/Documents/signage/content/nzdeliveryapp_digi-eyeline-1920x540.png
flutter: Is Image: true, Is Video: false
flutter: ====================================================
flutter: Storing content widget for schedule item: 75e20d80-47ed-4274-aca9-0251f2a182b5
flutter: LoggingService: Logged simple media display: 75e20d80-47ed-4274-aca9-0251f2a182b5
flutter: LoggingService: Processing queue - Activities: 0, Proof of Play: 3
flutter: LoggingService: Processing 3 proof of play logs
flutter: LoggingService: Sending proof of play log to Supabase: ProofOfPlay(id: null, screenId: c26fd436-f692-4bc1-853d-ee9ee478b94f, campaignId: fe2fddc8-f709-4b54-b6c9-c95bf1ed9852, slideId: null, mediaId: 104a97fe-2928-4a35-b785-fbdc83765845, logDatetime: 2025-05-22 10:26:48.925335Z)
flutter: [SupabaseService] Logging proof of play: ProofOfPlay(id: null, screenId: c26fd436-f692-4bc1-853d-ee9ee478b94f, campaignId: fe2fddc8-f709-4b54-b6c9-c95bf1ed9852, slideId: null, mediaId: 104a97fe-2928-4a35-b785-fbdc83765845, logDatetime: 2025-05-22 10:26:48.925335Z)
flutter: [SupabaseService] Proof of play data: {screen_id: c26fd436-f692-4bc1-853d-ee9ee478b94f, log_datetime: 2025-05-22T10:26:48.925335Z, campaign_id: fe2fddc8-f709-4b54-b6c9-c95bf1ed9852, slide_id: null, media_id: 104a97fe-2928-4a35-b785-fbdc83765845}
flutter: ====================================================
flutter: Creating SimpleMediaWidget for promo1.jpg
flutter: ID: b81b47c5-8469-4ae8-9980-d273a53bccc9
flutter: File Path: /home/<USER>/Documents/signage/content/promo1.jpg
flutter: Is Image: true, Is Video: false
flutter: ====================================================
flutter: Storing content widget for schedule item: b81b47c5-8469-4ae8-9980-d273a53bccc9
flutter: LoggingService: Logged simple media display: b81b47c5-8469-4ae8-9980-d273a53bccc9
flutter: [SupabaseService] Proof of play log response: [{id: 588, screen_id: c26fd436-f692-4bc1-853d-ee9ee478b94f, campaign_id: fe2fddc8-f709-4b54-b6c9-c95bf1ed9852, slide_id: null, media_id: 104a97fe-2928-4a35-b785-fbdc83765845, log_datetime: 2025-05-22T10:26:48.925335+00:00}]
flutter: [SupabaseService] Logging proof of play: ProofOfPlay(id: null, screenId: c26fd436-f692-4bc1-853d-ee9ee478b94f, campaignId: fe2fddc8-f709-4b54-b6c9-c95bf1ed9852, slideId: b13177ab-b613-46fc-9b8e-7eec420c53b9, mediaId: null, logDatetime: 2025-05-22 10:26:48.949636Z)
flutter: [SupabaseService] Setting media_id to null for empty value
flutter: [SupabaseService] Proof of play data: {screen_id: c26fd436-f692-4bc1-853d-ee9ee478b94f, log_datetime: 2025-05-22T10:26:48.949636Z, campaign_id: fe2fddc8-f709-4b54-b6c9-c95bf1ed9852, slide_id: b13177ab-b613-46fc-9b8e-7eec420c53b9, media_id: null}
flutter: [SupabaseService] Proof of play log response: [{id: 589, screen_id: c26fd436-f692-4bc1-853d-ee9ee478b94f, campaign_id: fe2fddc8-f709-4b54-b6c9-c95bf1ed9852, slide_id: b13177ab-b613-46fc-9b8e-7eec420c53b9, media_id: null, log_datetime: 2025-05-22T10:26:48.949636+00:00}]
flutter: [SupabaseService] Logging proof of play: ProofOfPlay(id: null, screenId: c26fd436-f692-4bc1-853d-ee9ee478b94f, campaignId: fe2fddc8-f709-4b54-b6c9-c95bf1ed9852, slideId: null, mediaId: 75e20d80-47ed-4274-aca9-0251f2a182b5, logDatetime: 2025-05-22 10:26:49.135965Z)
flutter: [SupabaseService] Proof of play data: {screen_id: c26fd436-f692-4bc1-853d-ee9ee478b94f, log_datetime: 2025-05-22T10:26:49.135965Z, campaign_id: fe2fddc8-f709-4b54-b6c9-c95bf1ed9852, slide_id: null, media_id: 75e20d80-47ed-4274-aca9-0251f2a182b5}
flutter: [SupabaseService] Proof of play log response: [{id: 590, screen_id: c26fd436-f692-4bc1-853d-ee9ee478b94f, campaign_id: fe2fddc8-f709-4b54-b6c9-c95bf1ed9852, slide_id: null, media_id: 75e20d80-47ed-4274-aca9-0251f2a182b5, log_datetime: 2025-05-22T10:26:49.135965+00:00}]
flutter: LoggingService: Queue is already being processed, skipping
flutter: Displaying content widget for schedule item: b81b47c5-8469-4ae8-9980-d273a53bccc9
flutter: SimpleMediaWidget: initState for promo1.jpg
flutter: ImageTimerWidget: initState for /home/<USER>/Documents/signage/content/promo1.jpg
flutter: ImageTimerWidget: starting timer for /home/<USER>/Documents/signage/content/promo1.jpg with duration 8 seconds
flutter: ImageTimerWidget: image loaded for /home/<USER>/Documents/signage/content/promo1.jpg
flutter: APIWidget: dispose for e53e3897-8ef3-4b92-b1f8-962c2737074b
flutter: APIWidget: No video controllers to clean up
flutter: APIWidget: All resources cleaned up
flutter: SlideShowWidget: dispose for Fire Index
flutter: SlideShowWidget: Disposing API data controller
flutter: ApiDataController: Disposing
flutter: LoggingService: Proof of play log result: true
flutter: LoggingService: Sending proof of play log to Supabase: ProofOfPlay(id: null, screenId: c26fd436-f692-4bc1-853d-ee9ee478b94f, campaignId: fe2fddc8-f709-4b54-b6c9-c95bf1ed9852, slideId: b13177ab-b613-46fc-9b8e-7eec420c53b9, mediaId: null, logDatetime: 2025-05-22 10:26:48.949636Z)
flutter: LoggingService: Proof of play log result: true
flutter: LoggingService: Sending proof of play log to Supabase: ProofOfPlay(id: null, screenId: c26fd436-f692-4bc1-853d-ee9ee478b94f, campaignId: fe2fddc8-f709-4b54-b6c9-c95bf1ed9852, slideId: null, mediaId: 75e20d80-47ed-4274-aca9-0251f2a182b5, logDatetime: 2025-05-22 10:26:49.135965Z)
flutter: LoggingService: Proof of play log result: true
flutter: LoggingService: Processing queue - Activities: 0, Proof of Play: 1
flutter: LoggingService: Processing 1 proof of play logs
flutter: LoggingService: Sending proof of play log to Supabase: ProofOfPlay(id: null, screenId: c26fd436-f692-4bc1-853d-ee9ee478b94f, campaignId: fe2fddc8-f709-4b54-b6c9-c95bf1ed9852, slideId: null, mediaId: b81b47c5-8469-4ae8-9980-d273a53bccc9, logDatetime: 2025-05-22 10:26:49.143980Z)
flutter: [SupabaseService] Logging proof of play: ProofOfPlay(id: null, screenId: c26fd436-f692-4bc1-853d-ee9ee478b94f, campaignId: fe2fddc8-f709-4b54-b6c9-c95bf1ed9852, slideId: null, mediaId: b81b47c5-8469-4ae8-9980-d273a53bccc9, logDatetime: 2025-05-22 10:26:49.143980Z)
flutter: [SupabaseService] Proof of play data: {screen_id: c26fd436-f692-4bc1-853d-ee9ee478b94f, log_datetime: 2025-05-22T10:26:49.143980Z, campaign_id: fe2fddc8-f709-4b54-b6c9-c95bf1ed9852, slide_id: null, media_id: b81b47c5-8469-4ae8-9980-d273a53bccc9}
flutter: [SupabaseService] Proof of play log response: [{id: 591, screen_id: c26fd436-f692-4bc1-853d-ee9ee478b94f, campaign_id: fe2fddc8-f709-4b54-b6c9-c95bf1ed9852, slide_id: null, media_id: b81b47c5-8469-4ae8-9980-d273a53bccc9, log_datetime: 2025-05-22T10:26:49.14398+00:00}]
flutter: LoggingService: Proof of play log result: true
flutter: Safety timer: disposing old content widget: b81b47c5-8469-4ae8-9980-d273a53bccc9
flutter: ====================================================
flutter: Disposing content widget for schedule item: b81b47c5-8469-4ae8-9980-d273a53bccc9
flutter: Current active schedule item ID: b81b47c5-8469-4ae8-9980-d273a53bccc9
flutter: Current schedule item ID: b81b47c5-8469-4ae8-9980-d273a53bccc9
flutter: Content widgets in memory: b81b47c5-8469-4ae8-9980-d273a53bccc9, 5c1fa150-ec88-4b65-8693-003c728939e5, 104a97fe-2928-4a35-b785-fbdc83765845, b13177ab-b613-46fc-9b8e-7eec420c53b9, 75e20d80-47ed-4274-aca9-0251f2a182b5
flutter: Content widget disposed for schedule item: b81b47c5-8469-4ae8-9980-d273a53bccc9
flutter: Remaining widgets in memory: 5c1fa150-ec88-4b65-8693-003c728939e5, 104a97fe-2928-4a35-b785-fbdc83765845, b13177ab-b613-46fc-9b8e-7eec420c53b9, 75e20d80-47ed-4274-aca9-0251f2a182b5
flutter: ====================================================
flutter: Safety timer: disposing old content widget: 5c1fa150-ec88-4b65-8693-003c728939e5
flutter: ====================================================
flutter: Disposing content widget for schedule item: 5c1fa150-ec88-4b65-8693-003c728939e5
flutter: Current active schedule item ID: b81b47c5-8469-4ae8-9980-d273a53bccc9
flutter: Current schedule item ID: b81b47c5-8469-4ae8-9980-d273a53bccc9
flutter: Content widgets in memory: 5c1fa150-ec88-4b65-8693-003c728939e5, 104a97fe-2928-4a35-b785-fbdc83765845, b13177ab-b613-46fc-9b8e-7eec420c53b9, 75e20d80-47ed-4274-aca9-0251f2a182b5
flutter: Content widget disposed for schedule item: 5c1fa150-ec88-4b65-8693-003c728939e5
flutter: Remaining widgets in memory: 104a97fe-2928-4a35-b785-fbdc83765845, b13177ab-b613-46fc-9b8e-7eec420c53b9, 75e20d80-47ed-4274-aca9-0251f2a182b5
flutter: ====================================================
flutter: Safety timer: disposing old content widget: 104a97fe-2928-4a35-b785-fbdc83765845
flutter: ====================================================
flutter: Disposing content widget for schedule item: 104a97fe-2928-4a35-b785-fbdc83765845
flutter: Current active schedule item ID: b81b47c5-8469-4ae8-9980-d273a53bccc9
flutter: Current schedule item ID: b81b47c5-8469-4ae8-9980-d273a53bccc9
flutter: Content widgets in memory: 104a97fe-2928-4a35-b785-fbdc83765845, b13177ab-b613-46fc-9b8e-7eec420c53b9, 75e20d80-47ed-4274-aca9-0251f2a182b5
flutter: Content widget disposed for schedule item: 104a97fe-2928-4a35-b785-fbdc83765845
flutter: Remaining widgets in memory: b13177ab-b613-46fc-9b8e-7eec420c53b9, 75e20d80-47ed-4274-aca9-0251f2a182b5
flutter: ====================================================
flutter: AppExitService: Exiting app on platform: Linux
flutter: AppExitService: Exiting desktop app with exit(0)
Lost connection to device.