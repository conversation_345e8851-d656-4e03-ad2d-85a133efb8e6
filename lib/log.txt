Launching lib/main.dart on Linux in debug mode...
Building Linux application...                                           
✓ Built build/linux/x64/debug/bundle/signage
Monitor 0: 1920x1080
Total dimensions: 1920x1080
package:media_kit_libs_linux registered.
flutter: Detected 1 monitors:
flutter: Monitor 0: (0.0, 0.0) 1920.0x1080.0, isPrimary: true
flutter: Total dimensions: 1920.0 x 1080.0
Syncing files to device Linux...                                   247ms

Flutter run key commands.
r Hot reload. 🔥🔥🔥
R Hot restart.
h List all available interactive commands.
d Detach (terminate "flutter run" but leave application running).
c Clear the screen
q Quit (terminate the application on the device).

A Dart VM Service on Linux is available at: http://127.0.0.1:34989/-D7YemZ310o=/
The Flutter DevTools debugger and profiler on Linux is available at: http://127.0.0.1:9101?uri=http://127.0.0.1:34989/-D7YemZ310o=/
flutter: supabase.supabase_flutter: INFO: ***** Supabase init completed ***** 
flutter: [SupabaseService] Supabase initialized with realtime enabled
flutter: media_kit: NativeReferenceHolder: Allocated 127575898897712
flutter: Created signage directory structure at: /home/<USER>/Documents/signage
flutter: Content directory: /home/<USER>/Documents/signage/content
flutter: Data directory: /home/<USER>/Documents/signage/data
flutter: Temp directory: /home/<USER>/Documents/signage/temp
flutter: CursorManager: Hiding cursor
flutter: Starting player controller service
flutter: PlayerControllerService: Starting background service
flutter: Starting logging service
flutter: LoggingService: Starting
flutter: PlayerScheduleService: Parsed start time: 08:00:00
flutter: PlayerScheduleService: Parsed end time: 23:30:00
flutter: PlayerScheduleService: Current time: 14:56:47
flutter: PlayerScheduleService: Start time: 08:00:00
flutter: PlayerScheduleService: End time: 23:30:00
flutter: PlayerScheduleService: Current time is between start and end time - starting content display
flutter: PlayerScheduleService: Starting content display
flutter: PlayerScheduleService: Start content callback received
flutter: PlayerScheduleService: Setting stop timer for 30792 seconds (8h 33m 12s)
flutter: PlayerScheduleService: Service started
flutter: PlayerControllerService: Loaded settings for screen c26fd436-f692-4bc1-853d-ee9ee478b94f
flutter: PlayerControllerService: Update frequency: 00:05:00
flutter: PlayerControllerService: Checking trial/subscription expiration
flutter: PlayerControllerService: Current time: 2025-05-31T09:26:47.159087Z
flutter: PlayerControllerService: Trial ends at: 2025-06-06T17:17:43.262846Z
flutter: PlayerControllerService: Subscription status: trial
flutter: PlayerControllerService: Trial/subscription is still active
flutter: PlayerControllerService: Setting update timer to 300 seconds
flutter: PlayerControllerService: Subscribing to realtime updates for screen c26fd436-f692-4bc1-853d-ee9ee478b94f
flutter: PlayerControllerService: Using channel name: public:screens:c26fd436-f692-4bc1-853d-ee9ee478b94f
flutter: PlayerControllerService: Testing realtime connection
flutter: PlayerControllerService: Subscription is active
flutter: PlayerControllerService: Channel name: public:screens:c26fd436-f692-4bc1-853d-ee9ee478b94f
flutter: PlayerControllerService: Listening for updates to screen c26fd436-f692-4bc1-853d-ee9ee478b94f
flutter: LoggingService: Loaded settings for screen c26fd436-f692-4bc1-853d-ee9ee478b94f
flutter: LoggingService: Loaded screen ID: c26fd436-f692-4bc1-853d-ee9ee478b94f
flutter: LoggingService: Logging player start
flutter: LoggingService: Logged player start
flutter: LoggingService: Processing queue - Activities: 1, Proof of Play: 0
flutter: LoggingService: Processing 1 activity logs
flutter: LoggingService: Sending activity log to Supabase: Player Start
flutter: [SupabaseService] Logging screen activity: Player Start
flutter: [SupabaseService] Activity data: {screen_id: c26fd436-f692-4bc1-853d-ee9ee478b94f, log_details: Player Start, log_datetime: 2025-05-31T09:26:47.246224Z}
flutter: LoggingService: Queue is already being processed, skipping
flutter: Loaded state: campaign=0, schedule=2
flutter: Playing schedule item: FBN2407-VRefresh-BlackcurrantYuZu-1920x540-v1.mp4 (type: SimpleMedia)
flutter: No current widget (first content or null widget)
flutter: ====================================================
flutter: Creating SimpleMediaWidget for FBN2407-VRefresh-BlackcurrantYuZu-1920x540-v1.mp4
flutter: ID: 104a97fe-2928-4a35-b785-fbdc83765845
flutter: File Path: /home/<USER>/Documents/signage/content/FBN2407-VRefresh-BlackcurrantYuZu-1920x540-v1.mp4
flutter: Is Image: false, Is Video: true
flutter: ====================================================
flutter: Storing content widget for schedule item: 104a97fe-2928-4a35-b785-fbdc83765845
flutter: LoggingService: Logged simple media display: 104a97fe-2928-4a35-b785-fbdc83765845
flutter: LoggingService: Queue is already being processed, skipping
flutter: Displaying content widget for schedule item: 104a97fe-2928-4a35-b785-fbdc83765845
flutter: SimpleMediaWidget: initState for FBN2407-VRefresh-BlackcurrantYuZu-1920x540-v1.mp4
media_kit: VideoOutput: video_output_new: 127575908829072
Cannot load libcuda.so.1
media_kit: VideoOutput: Using H/W rendering.
flutter: VideoOutput.Resize
flutter: {handle: 127575908829072, id: 104489888192352, rect: {left: 0, top: 0, width: 1, height: 1}}
flutter: NativeVideoController: Texture ID: 104489888192352
flutter: [SupabaseService] Screen activity log response: [{id: 229, screen_id: c26fd436-f692-4bc1-853d-ee9ee478b94f, log_details: Player Start, log_datetime: 2025-05-31T09:26:47.246224+00:00, file_download_count: null, total_file_download: null}]
flutter: LoggingService: Activity log result: true
flutter: LoggingService: Processing 1 proof of play logs
flutter: LoggingService: Sending proof of play log to Supabase: ProofOfPlay(id: null, screenId: c26fd436-f692-4bc1-853d-ee9ee478b94f, campaignId: fe2fddc8-f709-4b54-b6c9-c95bf1ed9852, slideId: null, mediaId: 104a97fe-2928-4a35-b785-fbdc83765845, logDatetime: 2025-05-31 09:26:47.610179Z)
flutter: [SupabaseService] Logging proof of play: ProofOfPlay(id: null, screenId: c26fd436-f692-4bc1-853d-ee9ee478b94f, campaignId: fe2fddc8-f709-4b54-b6c9-c95bf1ed9852, slideId: null, mediaId: 104a97fe-2928-4a35-b785-fbdc83765845, logDatetime: 2025-05-31 09:26:47.610179Z)
flutter: [SupabaseService] Proof of play data: {screen_id: c26fd436-f692-4bc1-853d-ee9ee478b94f, log_datetime: 2025-05-31T09:26:47.610179Z, campaign_id: fe2fddc8-f709-4b54-b6c9-c95bf1ed9852, slide_id: null, media_id: 104a97fe-2928-4a35-b785-fbdc83765845}
flutter: [SupabaseService] Proof of play log response: [{id: 845, screen_id: c26fd436-f692-4bc1-853d-ee9ee478b94f, campaign_id: fe2fddc8-f709-4b54-b6c9-c95bf1ed9852, slide_id: null, media_id: 104a97fe-2928-4a35-b785-fbdc83765845, log_datetime: 2025-05-31T09:26:47.610179+00:00}]
flutter: LoggingService: Proof of play log result: true
media_kit: TextureGL: Resize: (1920, 540)
flutter: VideoOutput.Resize
flutter: {handle: 127575908829072, id: 104489888192352, rect: {left: 0, top: 0, width: 1920, height: 540}}
flutter: LoggingService: Processing queue - Activities: 0, Proof of Play: 0
flutter: PlatformVideoWidget: Desktop video completed
flutter: ====================================================
flutter: Content completed, moving to next item
flutter: Completed widget details:
flutter: Name: FBN2407-VRefresh-BlackcurrantYuZu-1920x540-v1.mp4
flutter: ID: 104a97fe-2928-4a35-b785-fbdc83765845
flutter: Type: SimpleMedia
flutter: Media Type: Video
flutter: ====================================================
flutter: Moving from schedule item 2 to 3
flutter: New schedule item: nzdeliveryapp_digi-eyeline-1920x540.png
flutter: ====================================================
flutter: Playing next schedule item: nzdeliveryapp_digi-eyeline-1920x540.png
flutter: ID: 75e20d80-47ed-4274-aca9-0251f2a182b5
flutter: ====================================================
flutter: ====================================================
flutter: Starting transition to next content: nzdeliveryapp_digi-eyeline-1920x540.png
flutter: Current content: 104a97fe-2928-4a35-b785-fbdc83765845
flutter: Next content: 75e20d80-47ed-4274-aca9-0251f2a182b5
flutter: ====================================================
flutter: ====================================================
flutter: Current widget that has finished playing:
flutter: Name: nzdeliveryapp_digi-eyeline-1920x540.png
flutter: ID: 75e20d80-47ed-4274-aca9-0251f2a182b5
flutter: Type: SimpleMedia
flutter: ====================================================
flutter: Saved state: campaign=0, schedule=3
flutter: ====================================================
flutter: Creating SimpleMediaWidget for nzdeliveryapp_digi-eyeline-1920x540.png
flutter: ID: 75e20d80-47ed-4274-aca9-0251f2a182b5
flutter: File Path: /home/<USER>/Documents/signage/content/nzdeliveryapp_digi-eyeline-1920x540.png
flutter: Is Image: true, Is Video: false
flutter: ====================================================
flutter: Storing content widget for schedule item: 75e20d80-47ed-4274-aca9-0251f2a182b5
flutter: LoggingService: Logged simple media display: 75e20d80-47ed-4274-aca9-0251f2a182b5
flutter: LoggingService: Processing queue - Activities: 0, Proof of Play: 1
flutter: LoggingService: Processing 1 proof of play logs
flutter: LoggingService: Sending proof of play log to Supabase: ProofOfPlay(id: null, screenId: c26fd436-f692-4bc1-853d-ee9ee478b94f, campaignId: fe2fddc8-f709-4b54-b6c9-c95bf1ed9852, slideId: null, mediaId: 75e20d80-47ed-4274-aca9-0251f2a182b5, logDatetime: 2025-05-31 09:27:03.198684Z)
flutter: [SupabaseService] Logging proof of play: ProofOfPlay(id: null, screenId: c26fd436-f692-4bc1-853d-ee9ee478b94f, campaignId: fe2fddc8-f709-4b54-b6c9-c95bf1ed9852, slideId: null, mediaId: 75e20d80-47ed-4274-aca9-0251f2a182b5, logDatetime: 2025-05-31 09:27:03.198684Z)
flutter: [SupabaseService] Proof of play data: {screen_id: c26fd436-f692-4bc1-853d-ee9ee478b94f, log_datetime: 2025-05-31T09:27:03.198684Z, campaign_id: fe2fddc8-f709-4b54-b6c9-c95bf1ed9852, slide_id: null, media_id: 75e20d80-47ed-4274-aca9-0251f2a182b5}
flutter: Starting transition animation
flutter: Displaying content widget for schedule item: 75e20d80-47ed-4274-aca9-0251f2a182b5
flutter: SimpleMediaWidget: initState for nzdeliveryapp_digi-eyeline-1920x540.png
flutter: ImageTimerWidget: initState for /home/<USER>/Documents/signage/content/nzdeliveryapp_digi-eyeline-1920x540.png
flutter: ImageTimerWidget: starting timer for /home/<USER>/Documents/signage/content/nzdeliveryapp_digi-eyeline-1920x540.png with duration 8 seconds
flutter: Displaying content widget for schedule item: 75e20d80-47ed-4274-aca9-0251f2a182b5
flutter: Displaying content widget for schedule item: 75e20d80-47ed-4274-aca9-0251f2a182b5
flutter: ImageTimerWidget: image loaded for /home/<USER>/Documents/signage/content/nzdeliveryapp_digi-eyeline-1920x540.png
flutter: Displaying content widget for schedule item: 75e20d80-47ed-4274-aca9-0251f2a182b5
flutter: ====================================================
flutter: Completing transition
flutter: Current content ID: 75e20d80-47ed-4274-aca9-0251f2a182b5
flutter: Next content ID: 75e20d80-47ed-4274-aca9-0251f2a182b5
flutter: ====================================================
flutter: Displaying content widget for schedule item: 75e20d80-47ed-4274-aca9-0251f2a182b5
flutter: ImageTimerWidget: image loaded for /home/<USER>/Documents/signage/content/nzdeliveryapp_digi-eyeline-1920x540.png
flutter: PlatformVideoWidget: Disposing video player
flutter: SimpleMediaWidget: dispose for FBN2407-VRefresh-BlackcurrantYuZu-1920x540-v1.mp4
media_kit: VideoOutput: video_output_dispose: 127575908829072
flutter: [SupabaseService] Proof of play log response: [{id: 846, screen_id: c26fd436-f692-4bc1-853d-ee9ee478b94f, campaign_id: fe2fddc8-f709-4b54-b6c9-c95bf1ed9852, slide_id: null, media_id: 75e20d80-47ed-4274-aca9-0251f2a182b5, log_datetime: 2025-05-31T09:27:03.198684+00:00}]
flutter: LoggingService: Proof of play log result: true
flutter: LoggingService: Processing queue - Activities: 0, Proof of Play: 0
flutter: Safety timer: disposing old content widget: 104a97fe-2928-4a35-b785-fbdc83765845
flutter: ====================================================
flutter: Disposing content widget for schedule item: 104a97fe-2928-4a35-b785-fbdc83765845
flutter: Current active schedule item ID: 75e20d80-47ed-4274-aca9-0251f2a182b5
flutter: Current schedule item ID: 75e20d80-47ed-4274-aca9-0251f2a182b5
flutter: Content widgets in memory: 104a97fe-2928-4a35-b785-fbdc83765845, 75e20d80-47ed-4274-aca9-0251f2a182b5
flutter: Content widget disposed for schedule item: 104a97fe-2928-4a35-b785-fbdc83765845
flutter: Remaining widgets in memory: 75e20d80-47ed-4274-aca9-0251f2a182b5
flutter: ====================================================
flutter: ImageTimerWidget: timer completed for /home/<USER>/Documents/signage/content/nzdeliveryapp_digi-eyeline-1920x540.png
flutter: ====================================================
flutter: Content completed, moving to next item
flutter: Completed widget details:
flutter: Name: nzdeliveryapp_digi-eyeline-1920x540.png
flutter: ID: 75e20d80-47ed-4274-aca9-0251f2a182b5
flutter: Type: SimpleMedia
flutter: Media Type: Image
flutter: ====================================================
flutter: Moving from schedule item 3 to 4
flutter: New schedule item: 37327 Skinny Eyeline digital 1920x540.mp4
flutter: ====================================================
flutter: Playing next schedule item: 37327 Skinny Eyeline digital 1920x540.mp4
flutter: ID: d696c03d-c48b-420e-9857-917a226024c4
flutter: ====================================================
flutter: ====================================================
flutter: Starting transition to next content: 37327 Skinny Eyeline digital 1920x540.mp4
flutter: Current content: 75e20d80-47ed-4274-aca9-0251f2a182b5
flutter: Next content: d696c03d-c48b-420e-9857-917a226024c4
flutter: ====================================================
flutter: ====================================================
flutter: Current widget that has finished playing:
flutter: Name: 37327 Skinny Eyeline digital 1920x540.mp4
flutter: ID: d696c03d-c48b-420e-9857-917a226024c4
flutter: Type: SimpleMedia
flutter: ====================================================
flutter: Saved state: campaign=0, schedule=4
flutter: ====================================================
flutter: Creating SimpleMediaWidget for 37327 Skinny Eyeline digital 1920x540.mp4
flutter: ID: d696c03d-c48b-420e-9857-917a226024c4
flutter: File Path: /home/<USER>/Documents/signage/content/37327 Skinny Eyeline digital 1920x540.mp4
flutter: Is Image: false, Is Video: true
flutter: ====================================================
flutter: Storing content widget for schedule item: d696c03d-c48b-420e-9857-917a226024c4
flutter: LoggingService: Logged simple media display: d696c03d-c48b-420e-9857-917a226024c4
flutter: LoggingService: Processing queue - Activities: 0, Proof of Play: 1
flutter: LoggingService: Processing 1 proof of play logs
flutter: LoggingService: Sending proof of play log to Supabase: ProofOfPlay(id: null, screenId: c26fd436-f692-4bc1-853d-ee9ee478b94f, campaignId: fe2fddc8-f709-4b54-b6c9-c95bf1ed9852, slideId: null, mediaId: d696c03d-c48b-420e-9857-917a226024c4, logDatetime: 2025-05-31 09:27:11.261564Z)
flutter: [SupabaseService] Logging proof of play: ProofOfPlay(id: null, screenId: c26fd436-f692-4bc1-853d-ee9ee478b94f, campaignId: fe2fddc8-f709-4b54-b6c9-c95bf1ed9852, slideId: null, mediaId: d696c03d-c48b-420e-9857-917a226024c4, logDatetime: 2025-05-31 09:27:11.261564Z)
flutter: [SupabaseService] Proof of play data: {screen_id: c26fd436-f692-4bc1-853d-ee9ee478b94f, log_datetime: 2025-05-31T09:27:11.261564Z, campaign_id: fe2fddc8-f709-4b54-b6c9-c95bf1ed9852, slide_id: null, media_id: d696c03d-c48b-420e-9857-917a226024c4}
flutter: Starting transition animation
flutter: Displaying content widget for schedule item: d696c03d-c48b-420e-9857-917a226024c4
flutter: SimpleMediaWidget: initState for 37327 Skinny Eyeline digital 1920x540.mp4
flutter: ImageTimerWidget: image loaded for /home/<USER>/Documents/signage/content/nzdeliveryapp_digi-eyeline-1920x540.png
flutter: Displaying content widget for schedule item: d696c03d-c48b-420e-9857-917a226024c4
media_kit: VideoOutput: video_output_new: 127575900552464
Cannot load libcuda.so.1
media_kit: VideoOutput: Using H/W rendering.
flutter: VideoOutput.Resize
flutter: {handle: 127575900552464, id: 104489888751040, rect: {left: 0, top: 0, width: 1, height: 1}}
flutter: NativeVideoController: Texture ID: 104489888751040
flutter: Displaying content widget for schedule item: d696c03d-c48b-420e-9857-917a226024c4
flutter: [SupabaseService] Proof of play log response: [{id: 847, screen_id: c26fd436-f692-4bc1-853d-ee9ee478b94f, campaign_id: fe2fddc8-f709-4b54-b6c9-c95bf1ed9852, slide_id: null, media_id: d696c03d-c48b-420e-9857-917a226024c4, log_datetime: 2025-05-31T09:27:11.261564+00:00}]
flutter: LoggingService: Proof of play log result: true
flutter: Displaying content widget for schedule item: d696c03d-c48b-420e-9857-917a226024c4
flutter: ====================================================
flutter: Completing transition
flutter: Current content ID: d696c03d-c48b-420e-9857-917a226024c4
flutter: Next content ID: d696c03d-c48b-420e-9857-917a226024c4
flutter: ====================================================
flutter: Displaying content widget for schedule item: d696c03d-c48b-420e-9857-917a226024c4
flutter: ImageTimerWidget: dispose for /home/<USER>/Documents/signage/content/nzdeliveryapp_digi-eyeline-1920x540.png
flutter: ImageTimerWidget: canceling timer for /home/<USER>/Documents/signage/content/nzdeliveryapp_digi-eyeline-1920x540.png
flutter: SimpleMediaWidget: dispose for nzdeliveryapp_digi-eyeline-1920x540.png
media_kit: TextureGL: Resize: (1920, 540)
flutter: VideoOutput.Resize
flutter: {handle: 127575900552464, id: 104489888751040, rect: {left: 0, top: 0, width: 1920, height: 540}}
flutter: Safety timer: disposing old content widget: 75e20d80-47ed-4274-aca9-0251f2a182b5
flutter: ====================================================
flutter: Disposing content widget for schedule item: 75e20d80-47ed-4274-aca9-0251f2a182b5
flutter: Current active schedule item ID: d696c03d-c48b-420e-9857-917a226024c4
flutter: Current schedule item ID: d696c03d-c48b-420e-9857-917a226024c4
flutter: Content widgets in memory: 75e20d80-47ed-4274-aca9-0251f2a182b5, d696c03d-c48b-420e-9857-917a226024c4
flutter: Content widget disposed for schedule item: 75e20d80-47ed-4274-aca9-0251f2a182b5
flutter: Remaining widgets in memory: d696c03d-c48b-420e-9857-917a226024c4
flutter: ====================================================
flutter: PlatformVideoWidget: Desktop video completed
flutter: ====================================================
flutter: Content completed, moving to next item
flutter: Completed widget details:
flutter: Name: 37327 Skinny Eyeline digital 1920x540.mp4
flutter: ID: d696c03d-c48b-420e-9857-917a226024c4
flutter: Type: SimpleMedia
flutter: Media Type: Video
flutter: ====================================================
flutter: Last item in schedule, preparing to loop back to first item
flutter: Resetting from schedule item 4 to 0
flutter: First schedule item: BetterWithPepsi.mp4
flutter: ====================================================
flutter: Playing first schedule item: BetterWithPepsi.mp4
flutter: ID: 5c1fa150-ec88-4b65-8693-003c728939e5
flutter: (Looping back to first item)
flutter: ====================================================
flutter: ====================================================
flutter: Starting transition to first content: BetterWithPepsi.mp4
flutter: Current content: d696c03d-c48b-420e-9857-917a226024c4
flutter: Next content: 5c1fa150-ec88-4b65-8693-003c728939e5
flutter: ====================================================
flutter: ====================================================
flutter: Current widget that has finished playing:
flutter: Name: BetterWithPepsi.mp4
flutter: ID: 5c1fa150-ec88-4b65-8693-003c728939e5
flutter: Type: SimpleMedia
flutter: ====================================================
flutter: Saved state: campaign=0, schedule=0
flutter: ====================================================
flutter: Creating SimpleMediaWidget for BetterWithPepsi.mp4
flutter: ID: 5c1fa150-ec88-4b65-8693-003c728939e5
flutter: File Path: /home/<USER>/Documents/signage/content/BetterWithPepsi.mp4
flutter: Is Image: false, Is Video: true
flutter: ====================================================
flutter: Storing content widget for schedule item: 5c1fa150-ec88-4b65-8693-003c728939e5
flutter: LoggingService: Logged simple media display: 5c1fa150-ec88-4b65-8693-003c728939e5
flutter: LoggingService: Processing queue - Activities: 0, Proof of Play: 1
flutter: LoggingService: Processing 1 proof of play logs
flutter: LoggingService: Sending proof of play log to Supabase: ProofOfPlay(id: null, screenId: c26fd436-f692-4bc1-853d-ee9ee478b94f, campaignId: fe2fddc8-f709-4b54-b6c9-c95bf1ed9852, slideId: null, mediaId: 5c1fa150-ec88-4b65-8693-003c728939e5, logDatetime: 2025-05-31 09:27:16.937755Z)
flutter: [SupabaseService] Logging proof of play: ProofOfPlay(id: null, screenId: c26fd436-f692-4bc1-853d-ee9ee478b94f, campaignId: fe2fddc8-f709-4b54-b6c9-c95bf1ed9852, slideId: null, mediaId: 5c1fa150-ec88-4b65-8693-003c728939e5, logDatetime: 2025-05-31 09:27:16.937755Z)
flutter: [SupabaseService] Proof of play data: {screen_id: c26fd436-f692-4bc1-853d-ee9ee478b94f, log_datetime: 2025-05-31T09:27:16.937755Z, campaign_id: fe2fddc8-f709-4b54-b6c9-c95bf1ed9852, slide_id: null, media_id: 5c1fa150-ec88-4b65-8693-003c728939e5}
flutter: Starting transition animation
flutter: Displaying content widget for schedule item: 5c1fa150-ec88-4b65-8693-003c728939e5
flutter: SimpleMediaWidget: initState for BetterWithPepsi.mp4
media_kit: VideoOutput: video_output_new: 127575910431264
Cannot load libcuda.so.1
media_kit: VideoOutput: Using H/W rendering.
flutter: VideoOutput.Resize
flutter: {handle: 127575910431264, id: 104489891596944, rect: {left: 0, top: 0, width: 1, height: 1}}
flutter: NativeVideoController: Texture ID: 104489891596944
flutter: [SupabaseService] Proof of play log response: [{id: 848, screen_id: c26fd436-f692-4bc1-853d-ee9ee478b94f, campaign_id: fe2fddc8-f709-4b54-b6c9-c95bf1ed9852, slide_id: null, media_id: 5c1fa150-ec88-4b65-8693-003c728939e5, log_datetime: 2025-05-31T09:27:16.937755+00:00}]
flutter: LoggingService: Proof of play log result: true
flutter: Displaying content widget for schedule item: 5c1fa150-ec88-4b65-8693-003c728939e5
flutter: Displaying content widget for schedule item: 5c1fa150-ec88-4b65-8693-003c728939e5
flutter: Displaying content widget for schedule item: 5c1fa150-ec88-4b65-8693-003c728939e5
flutter: ====================================================
flutter: Completing transition
flutter: Current content ID: 5c1fa150-ec88-4b65-8693-003c728939e5
flutter: Next content ID: 5c1fa150-ec88-4b65-8693-003c728939e5
flutter: ====================================================
flutter: Displaying content widget for schedule item: 5c1fa150-ec88-4b65-8693-003c728939e5
flutter: PlatformVideoWidget: Disposing video player
flutter: SimpleMediaWidget: dispose for 37327 Skinny Eyeline digital 1920x540.mp4
flutter: LoggingService: Processing queue - Activities: 0, Proof of Play: 0
media_kit: VideoOutput: video_output_dispose: 127575900552464
media_kit: TextureGL: Resize: (1920, 540)
flutter: VideoOutput.Resize
flutter: {handle: 127575910431264, id: 104489891596944, rect: {left: 0, top: 0, width: 1920, height: 540}}
flutter: Safety timer: disposing old content widget: d696c03d-c48b-420e-9857-917a226024c4
flutter: ====================================================
flutter: Disposing content widget for schedule item: d696c03d-c48b-420e-9857-917a226024c4
flutter: Current active schedule item ID: 5c1fa150-ec88-4b65-8693-003c728939e5
flutter: Current schedule item ID: 5c1fa150-ec88-4b65-8693-003c728939e5
flutter: Content widgets in memory: d696c03d-c48b-420e-9857-917a226024c4, 5c1fa150-ec88-4b65-8693-003c728939e5
flutter: Content widget disposed for schedule item: d696c03d-c48b-420e-9857-917a226024c4
flutter: Remaining widgets in memory: 5c1fa150-ec88-4b65-8693-003c728939e5
flutter: ====================================================
flutter: PlatformVideoWidget: Desktop video completed
flutter: ====================================================
flutter: Content completed, moving to next item
flutter: Completed widget details:
flutter: Name: BetterWithPepsi.mp4
flutter: ID: 5c1fa150-ec88-4b65-8693-003c728939e5
flutter: Type: SimpleMedia
flutter: Media Type: Video
flutter: ====================================================
flutter: Moving from schedule item 0 to 1
flutter: New schedule item: Weather
flutter: ====================================================
flutter: Playing next schedule item: Weather
flutter: ID: 80b479a8-d300-46ae-97d0-59653e52d73f
flutter: ====================================================
flutter: ====================================================
flutter: Starting transition to next content: Weather
flutter: Current content: 5c1fa150-ec88-4b65-8693-003c728939e5
flutter: Next content: 80b479a8-d300-46ae-97d0-59653e52d73f
flutter: ====================================================
flutter: ====================================================
flutter: Current widget that has finished playing:
flutter: Name: Weather
flutter: ID: 80b479a8-d300-46ae-97d0-59653e52d73f
flutter: Type: SlideShow
flutter: ====================================================
flutter: ====================================================
flutter: Creating SlideShowWidget for Weather
flutter: ID: 80b479a8-d300-46ae-97d0-59653e52d73f
flutter: ====================================================
flutter: Storing content widget for schedule item: 80b479a8-d300-46ae-97d0-59653e52d73f
flutter: LoggingService: Logged slide display: 80b479a8-d300-46ae-97d0-59653e52d73f
flutter: Starting transition animation
flutter: Saved state: campaign=0, schedule=1
flutter: Displaying content widget for schedule item: 80b479a8-d300-46ae-97d0-59653e52d73f
flutter: SlideShowWidget: initState for Weather
flutter: SlideShowWidget: Building content with design dimensions: 1920.0 x 1080.0
flutter: SlideShowWidget: Actual screen dimensions: 1920.0 x 1080.0
flutter: SlideShowWidget: Original playlist position: (0.0, 0.0), size: 1920.0 x 1080.0, zIndex: 1
flutter: SlideShowWidget: Expected scaled position: (0.0, 0.0), size: 1920.0 x 1080.0
flutter: SlideShowWidget: Original API position: (809.828125, 214.828125), size: 876.25 x 387.5, zIndex: 3
flutter: SlideShowWidget: Creating GLOBAL API data controller for all APIWidgets in this slide
flutter: ApiDataController: Created with apiUrl: https://api.openweathermap.org/data/2.5/weather?lat=17&lon=17&appid=29b1d3623a51675e8ed3c1d3fa13c0f0&units=metric, apiDataPreviewDuration: 0
flutter: ApiDataController: Fetching data from https://api.openweathermap.org/data/2.5/weather?lat=17&lon=17&appid=29b1d3623a51675e8ed3c1d3fa13c0f0&units=metric
flutter: ApiDataController: Setting up refresh timer for every 5 minutes
flutter: SlideShowWidget: GLOBAL API data controller created with apiDataPreviewDuration: 0
flutter: SlideShowWidget: Expected scaled API position: (809.828125, 214.828125), size: 876.25 x 387.5
flutter: SlideShowWidget: Original API position: (824.15625, 634.328125), size: 858.75 x 117.5, zIndex: 4
flutter: SlideShowWidget: Expected scaled API position: (824.15625, 634.328125), size: 858.75 x 117.5
flutter: SlideShowWidget: Original API position: (263.32916259765625, 258.32509358723956), size: 500.0 x 500.0, zIndex: 5
flutter: SlideShowWidget: Expected scaled API position: (263.32916259765625, 258.32509358723956), size: 500.0 x 500.0
flutter: Displaying content widget for schedule item: 80b479a8-d300-46ae-97d0-59653e52d73f
flutter: SlideShowWidget: Player screen size: 1920.0x1080.0
flutter: SlideShowWidget: Design size: 1920.0 x 1080.0
flutter: SlideShowWidget: Scaling factors: horizontal=1.0, vertical=1.0
flutter: SlideShowWidget: Building scaled widgets with:
flutter:   - Design dimensions: 1920.0 x 1080.0
flutter:   - Screen dimensions: 1920.0 x 1080.0
flutter:   - Scale factors: 1.0 x 1.0
flutter: SlideShowWidget: Scaling widget:
flutter:   - Original: left=0.0, top=0.0, width=1920.0, height=1080.0
flutter:   - Scaled: left=0.0, top=0.0, width=1920.0, height=1080.0
flutter: SlideShowWidget: Scaling widget:
flutter:   - Original: left=809.828125, top=214.828125, width=876.25, height=387.5
flutter:   - Scaled: left=809.828125, top=214.828125, width=876.25, height=387.5
flutter: SlideShowWidget: Scaling widget:
flutter:   - Original: left=824.15625, top=634.328125, width=858.75, height=117.5
flutter:   - Scaled: left=824.15625, top=634.328125, width=858.75, height=117.5
flutter: SlideShowWidget: Scaling widget:
flutter:   - Original: left=263.32916259765625, top=258.32509358723956, width=500.0, height=500.0
flutter:   - Scaled: left=263.32916259765625, top=258.32509358723956, width=500.0, height=500.0
flutter: PlaylistWidget: initState for 80b479a8-d300-46ae-97d0-59653e52d73f
flutter: PlaylistWidget: loading item weather-cloud.mp4 at index 0
flutter: APIWidget: initState for 5e11d095-702b-457c-bcec-db2adc7e5e91
flutter: APIWidget: Building widget with state - isLoading: true, hasError: false, isInitialized: false, contentWidgets: 0
flutter: APIWidget: Dimensions - width: 876.25, height: 387.5
flutter: APIWidget: Showing loading state
flutter: APIWidget: initState for 27a76786-c68e-4be7-9857-826c3a968cc4
flutter: APIWidget: Building widget with state - isLoading: true, hasError: false, isInitialized: false, contentWidgets: 0
flutter: APIWidget: Dimensions - width: 858.75, height: 117.5
flutter: APIWidget: Showing loading state
flutter: APIWidget: initState for dce9c75d-bae9-4160-ac4d-0d274fe8dc96
flutter: APIWidget: Building widget with state - isLoading: true, hasError: false, isInitialized: false, contentWidgets: 0
flutter: APIWidget: Dimensions - width: 500.0, height: 500.0
flutter: APIWidget: Showing loading state
flutter: Displaying content widget for schedule item: 80b479a8-d300-46ae-97d0-59653e52d73f
flutter: PlaylistWidget: Building with dimensions 1920.0 x 1080.0
media_kit: VideoOutput: video_output_new: 127575908616672
flutter: Displaying content widget for schedule item: 80b479a8-d300-46ae-97d0-59653e52d73f
media_kit: VideoOutput: Using H/W rendering.
Cannot load libcuda.so.1
flutter: VideoOutput.Resize
flutter: {handle: 127575908616672, id: 104489888492672, rect: {left: 0, top: 0, width: 1, height: 1}}
flutter: NativeVideoController: Texture ID: 104489888492672
flutter: ====================================================
flutter: Completing transition
flutter: Current content ID: 80b479a8-d300-46ae-97d0-59653e52d73f
flutter: Next content ID: 80b479a8-d300-46ae-97d0-59653e52d73f
flutter: ====================================================
flutter: Displaying content widget for schedule item: 80b479a8-d300-46ae-97d0-59653e52d73f
flutter: SlideShowWidget: Player screen size: 1920.0x1080.0
flutter: SlideShowWidget: Design size: 1920.0 x 1080.0
flutter: SlideShowWidget: Scaling factors: horizontal=1.0, vertical=1.0
flutter: SlideShowWidget: Building scaled widgets with:
flutter:   - Design dimensions: 1920.0 x 1080.0
flutter:   - Screen dimensions: 1920.0 x 1080.0
flutter:   - Scale factors: 1.0 x 1.0
flutter: SlideShowWidget: Scaling widget:
flutter:   - Original: left=0.0, top=0.0, width=1920.0, height=1080.0
flutter:   - Scaled: left=0.0, top=0.0, width=1920.0, height=1080.0
flutter: SlideShowWidget: Scaling widget:
flutter:   - Original: left=809.828125, top=214.828125, width=876.25, height=387.5
flutter:   - Scaled: left=809.828125, top=214.828125, width=876.25, height=387.5
flutter: SlideShowWidget: Scaling widget:
flutter:   - Original: left=824.15625, top=634.328125, width=858.75, height=117.5
flutter:   - Scaled: left=824.15625, top=634.328125, width=858.75, height=117.5
flutter: SlideShowWidget: Scaling widget:
flutter:   - Original: left=263.32916259765625, top=258.32509358723956, width=500.0, height=500.0
flutter:   - Scaled: left=263.32916259765625, top=258.32509358723956, width=500.0, height=500.0
flutter: PlaylistWidget: Building with dimensions 1920.0 x 1080.0
flutter: APIWidget: Building widget with state - isLoading: true, hasError: false, isInitialized: false, contentWidgets: 0
flutter: APIWidget: Dimensions - width: 876.25, height: 387.5
flutter: APIWidget: Showing loading state
flutter: APIWidget: Building widget with state - isLoading: true, hasError: false, isInitialized: false, contentWidgets: 0
flutter: APIWidget: Dimensions - width: 858.75, height: 117.5
flutter: APIWidget: Showing loading state
flutter: APIWidget: Building widget with state - isLoading: true, hasError: false, isInitialized: false, contentWidgets: 0
flutter: APIWidget: Dimensions - width: 500.0, height: 500.0
flutter: APIWidget: Showing loading state
flutter: PlatformVideoWidget: Disposing video player
flutter: SimpleMediaWidget: dispose for BetterWithPepsi.mp4
flutter: ApiDataController: API Response received with status 200
flutter: ApiDataController: Received 1 records from API
flutter: ApiDataController: API data fetched successfully, waiting for all APIWidgets to initialize
flutter: APIWidget: Controller update received for 5e11d095-702b-457c-bcec-db2adc7e5e91
flutter: APIWidget: Building content widgets for record index 0
flutter: APIWidget: Processing content item: {style: {color: rgba(255, 255, 255, 1), fontSize: 120px, textAlign: center, fontFamily: Helvetica, sans-serif, fontWeight: bold, verticalAlign: middle, backgroundColor: transparent}, subtype: api.text, dataField: weather.description, apiMapping: {weather.description: true}, placeholder: API Data, currentIndex: 0, placeholderUrl: }
flutter: APIWidget: Content item details - subtype: api.text, dataField: weather.description, placeholderUrl: 
flutter: APIWidget: Extracting field "weather.description" (parts: weather, description)
flutter: APIWidget: Found value for "weather.description": clear sky (String)
flutter: APIWidget: Widget dimensions - width: 876.25, height: 387.5
flutter: APIWidget: Building text widget with dataValue: clear sky (String)
flutter: APIWidget: Content config: {style: {color: rgba(255, 255, 255, 1), fontSize: 120px, textAlign: center, fontFamily: Helvetica, sans-serif, fontWeight: bold, verticalAlign: middle, backgroundColor: transparent}, subtype: api.text, dataField: weather.description, apiMapping: {weather.description: true}, placeholder: API Data, currentIndex: 0, placeholderUrl: }
flutter: APIWidget: Text content: "clear sky"
flutter: APIWidget: Text style: {color: rgba(255, 255, 255, 1), fontSize: 120px, textAlign: center, fontFamily: Helvetica, sans-serif, fontWeight: bold, verticalAlign: middle, backgroundColor: transparent}
flutter: APIWidget: Parsing color: rgba(255, 255, 255, 1)
flutter: APIWidget: Parsed rgba color: rgba(255, 255, 255, 1) -> Color(alpha: 1.0000, red: 1.0000, green: 1.0000, blue: 1.0000, colorSpace: ColorSpace.sRGB)
flutter: APIWidget: Text color from string: rgba(255, 255, 255, 1) -> Color(alpha: 1.0000, red: 1.0000, green: 1.0000, blue: 1.0000, colorSpace: ColorSpace.sRGB)
flutter: APIWidget: Raw fontSize value: 120px (String)
flutter: APIWidget: Parsed font size: 120.0
flutter: APIWidget: Scaled font size: 43.05555555555556 (scale factor: 0.3587962962962963)
flutter: APIWidget: Font weight: bold -> FontWeight.w700
flutter: APIWidget: Text alignment: center -> TextAlign.center
flutter: APIWidget: Creating text widget with content: "clear sky" at size 876.25 x 387.5
flutter: APIWidget: Added api.text widget with size 876.25x387.5
flutter: APIWidget: Built 1 content widgets
flutter: APIWidget: Controller update received for 27a76786-c68e-4be7-9857-826c3a968cc4
flutter: APIWidget: Building content widgets for record index 0
flutter: APIWidget: Processing content item: {style: {color: rgba(255, 255, 255, 1), fontSize: 96px, textAlign: center, fontFamily: Helvetica, sans-serif, fontWeight: bold, verticalAlign: middle, backgroundColor: transparent}, subtype: api.text, dataField: main.temp, apiMapping: {main.temp: true}, placeholder: API Data, currentIndex: 0, placeholderUrl: }
flutter: APIWidget: Content item details - subtype: api.text, dataField: main.temp, placeholderUrl: 
flutter: APIWidget: Extracting field "main.temp" (parts: main, temp)
flutter: APIWidget: Found value for "main.temp": 39.52 (double)
flutter: APIWidget: Widget dimensions - width: 858.75, height: 117.5
flutter: APIWidget: Building text widget with dataValue: 39.52 (double)
flutter: APIWidget: Content config: {style: {color: rgba(255, 255, 255, 1), fontSize: 96px, textAlign: center, fontFamily: Helvetica, sans-serif, fontWeight: bold, verticalAlign: middle, backgroundColor: transparent}, subtype: api.text, dataField: main.temp, apiMapping: {main.temp: true}, placeholder: API Data, currentIndex: 0, placeholderUrl: }
flutter: APIWidget: Text content: "39.52"
flutter: APIWidget: Text style: {color: rgba(255, 255, 255, 1), fontSize: 96px, textAlign: center, fontFamily: Helvetica, sans-serif, fontWeight: bold, verticalAlign: middle, backgroundColor: transparent}
flutter: APIWidget: Parsing color: rgba(255, 255, 255, 1)
flutter: APIWidget: Parsed rgba color: rgba(255, 255, 255, 1) -> Color(alpha: 1.0000, red: 1.0000, green: 1.0000, blue: 1.0000, colorSpace: ColorSpace.sRGB)
flutter: APIWidget: Text color from string: rgba(255, 255, 255, 1) -> Color(alpha: 1.0000, red: 1.0000, green: 1.0000, blue: 1.0000, colorSpace: ColorSpace.sRGB)
flutter: APIWidget: Raw fontSize value: 96px (String)
flutter: APIWidget: Parsed font size: 96.0
flutter: APIWidget: Scaled font size: 10.444444444444445 (scale factor: 0.1087962962962963)
flutter: APIWidget: Font weight: bold -> FontWeight.w700
flutter: APIWidget: Text alignment: center -> TextAlign.center
flutter: APIWidget: Creating text widget with content: "39.52" at size 858.75 x 117.5
flutter: APIWidget: Added api.text widget with size 858.75x117.5
flutter: APIWidget: Built 1 content widgets
flutter: APIWidget: Controller update received for dce9c75d-bae9-4160-ac4d-0d274fe8dc96
flutter: APIWidget: Building content widgets for record index 0
flutter: APIWidget: Processing content item: {style: {color: #000000, fontSize: 32px, textAlign: center, fontFamily: Inter, sans-serif, fontWeight: normal, verticalAlign: middle, backgroundColor: transparent}, subtype: api.image, dataField: weather.icon, placeholder: API Data, placeholderUrl: http://localhost:5000/src/assets/weather-icons/{weather.icon}.gif}
media_kit: VideoOutput: video_output_dispose: 127575910431264
media_kit: TextureGL: Resize: (1920, 1080)
flutter: APIWidget: Content item details - subtype: api.image, dataField: weather.icon, placeholderUrl: http://localhost:5000/src/assets/weather-icons/{weather.icon}.gif
flutter: APIWidget: Extracting field "weather.icon" (parts: weather, icon)
flutter: APIWidget: Found value for "weather.icon": 01d (String)
flutter: APIWidget: Widget dimensions - width: 500.0, height: 500.0
flutter: APIWidget: Building image widget with dataValue: 01d (String), placeholderUrl: http://localhost:5000/src/assets/weather-icons/{weather.icon}.gif
flutter: APIWidget: dataValue is String: 01d
flutter: APIWidget: URL validation check: 01d is invalid
flutter: APIWidget: Found field pattern {weather.icon} in placeholder URL
flutter: APIWidget: Using placeholder URL with substitution: http://localhost:5000/src/assets/weather-icons/{weather.icon}.gif -> http://localhost:5000/src/assets/weather-icons/01d.gif
flutter: APIWidget: Loading image from URL: http://localhost:5000/src/assets/weather-icons/01d.gif
flutter: APIWidget: Using direct streaming from remote server for image
flutter: APIWidget: Image type: Regular image
flutter: APIWidget: Image dimensions: 500.0 x 500.0
flutter: APIWidget: Rebuilt content for record 0 after sync
flutter: APIWidget: Rebuilt content for record 0 after sync
flutter: APIWidget: Added api.image widget with size 500.0x500.0
flutter: APIWidget: Built 1 content widgets
flutter: APIWidget: Rebuilt content for record 0 after sync
flutter: APIWidget: Building widget with state - isLoading: false, hasError: false, isInitialized: true, contentWidgets: 1
flutter: APIWidget: Dimensions - width: 876.25, height: 387.5
flutter: APIWidget: Displaying 1 content widgets
flutter: APIWidget: Building widget with state - isLoading: false, hasError: false, isInitialized: true, contentWidgets: 1
flutter: APIWidget: Dimensions - width: 858.75, height: 117.5
flutter: APIWidget: Displaying 1 content widgets
flutter: APIWidget: Building widget with state - isLoading: false, hasError: false, isInitialized: true, contentWidgets: 1
flutter: APIWidget: Dimensions - width: 500.0, height: 500.0
flutter: APIWidget: Displaying 1 content widgets
flutter: ApiDataController: Checking if all widgets are ready - dataReady: true, videoWidgets: 0
flutter: SlideShowWidget: All APIWidgets are ready, starting global timer
flutter: ApiDataController: Starting global timer for all APIWidgets
flutter: ApiDataController: Starting GLOBAL display timer - videoWidgets: 0, apiDataPreviewDuration: 0
flutter: ApiDataController: Starting GLOBAL display timer for 15 seconds (no videos)
flutter: APIWidget: Controller update received for 5e11d095-702b-457c-bcec-db2adc7e5e91
flutter: APIWidget: Controller update received for 27a76786-c68e-4be7-9857-826c3a968cc4
flutter: APIWidget: Controller update received for dce9c75d-bae9-4160-ac4d-0d274fe8dc96
flutter: APIWidget: Building widget with state - isLoading: false, hasError: false, isInitialized: true, contentWidgets: 1
flutter: APIWidget: Dimensions - width: 876.25, height: 387.5
flutter: APIWidget: Displaying 1 content widgets
flutter: APIWidget: Building widget with state - isLoading: false, hasError: false, isInitialized: true, contentWidgets: 1
flutter: APIWidget: Dimensions - width: 858.75, height: 117.5
flutter: APIWidget: Displaying 1 content widgets
flutter: APIWidget: Building widget with state - isLoading: false, hasError: false, isInitialized: true, contentWidgets: 1
flutter: APIWidget: Dimensions - width: 500.0, height: 500.0
flutter: APIWidget: Displaying 1 content widgets
flutter: VideoOutput.Resize
flutter: {handle: 127575908616672, id: 104489888492672, rect: {left: 0, top: 0, width: 1920, height: 1080}}
flutter: LoggingService: Processing queue - Activities: 0, Proof of Play: 1
flutter: LoggingService: Processing 1 proof of play logs
flutter: LoggingService: Sending proof of play log to Supabase: ProofOfPlay(id: null, screenId: c26fd436-f692-4bc1-853d-ee9ee478b94f, campaignId: fe2fddc8-f709-4b54-b6c9-c95bf1ed9852, slideId: 80b479a8-d300-46ae-97d0-59653e52d73f, mediaId: null, logDatetime: 2025-05-31 09:27:23.360039Z)
flutter: [SupabaseService] Logging proof of play: ProofOfPlay(id: null, screenId: c26fd436-f692-4bc1-853d-ee9ee478b94f, campaignId: fe2fddc8-f709-4b54-b6c9-c95bf1ed9852, slideId: 80b479a8-d300-46ae-97d0-59653e52d73f, mediaId: null, logDatetime: 2025-05-31 09:27:23.360039Z)
flutter: [SupabaseService] Setting media_id to null for empty value
flutter: [SupabaseService] Proof of play data: {screen_id: c26fd436-f692-4bc1-853d-ee9ee478b94f, log_datetime: 2025-05-31T09:27:23.360039Z, campaign_id: fe2fddc8-f709-4b54-b6c9-c95bf1ed9852, slide_id: 80b479a8-d300-46ae-97d0-59653e52d73f, media_id: null}
flutter: [SupabaseService] Proof of play log response: [{id: 849, screen_id: c26fd436-f692-4bc1-853d-ee9ee478b94f, campaign_id: fe2fddc8-f709-4b54-b6c9-c95bf1ed9852, slide_id: 80b479a8-d300-46ae-97d0-59653e52d73f, media_id: null, log_datetime: 2025-05-31T09:27:23.360039+00:00}]
flutter: LoggingService: Proof of play log result: true
flutter: CursorManager: Showing cursor
flutter: Safety timer: disposing old content widget: 5c1fa150-ec88-4b65-8693-003c728939e5
flutter: ====================================================
flutter: Disposing content widget for schedule item: 5c1fa150-ec88-4b65-8693-003c728939e5
flutter: Current active schedule item ID: 80b479a8-d300-46ae-97d0-59653e52d73f
flutter: Current schedule item ID: 80b479a8-d300-46ae-97d0-59653e52d73f
flutter: Content widgets in memory: 5c1fa150-ec88-4b65-8693-003c728939e5, 80b479a8-d300-46ae-97d0-59653e52d73f
flutter: Content widget disposed for schedule item: 5c1fa150-ec88-4b65-8693-003c728939e5
flutter: Remaining widgets in memory: 80b479a8-d300-46ae-97d0-59653e52d73f
flutter: ====================================================
flutter: CursorManager: Hiding cursor
flutter: AppExitService: Exiting app on platform: Linux
flutter: AppExitService: Exiting desktop app with exit(0)
Lost connection to device.