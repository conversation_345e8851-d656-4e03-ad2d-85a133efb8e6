import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:signage/core/models/campaign.dart';
import 'package:signage/core/models/media.dart';
import 'package:signage/core/models/schedule_item.dart';
import 'package:signage/core/storage/storage_service.dart';

/// Controller for managing campaign playback
class CampaignController with ChangeNotifier {
  List<Campaign> _campaigns = [];
  List<ScheduleItem> _scheduleItems = [];
  List<Media> _mediaItems = [];

  int _currentCampaignIndex = 0;
  int _currentScheduleIndex = 0;

  bool _isLoading = true;
  String _errorMessage = '';

  /// Get the current loading state
  bool get isLoading => _isLoading;

  /// Get the current error message
  String get errorMessage => _errorMessage;

  /// Get the list of campaigns
  List<Campaign> get campaigns => _campaigns;

  /// Get the list of schedule items
  List<ScheduleItem> get scheduleItems => _scheduleItems;

  /// Get the list of media items
  List<Media> get mediaItems => _mediaItems;

  /// Get the current campaign
  Campaign? get currentCampaign =>
      _campaigns.isNotEmpty && _currentCampaignIndex < _campaigns.length
          ? _campaigns[_currentCampaignIndex]
          : null;

  /// Get the current schedule item
  ScheduleItem? get currentScheduleItem =>
      _scheduleItems.isNotEmpty && _currentScheduleIndex < _scheduleItems.length
          ? _scheduleItems[_currentScheduleIndex]
          : null;

  /// Get the current schedule index
  int get currentScheduleIndex => _currentScheduleIndex;

  /// Initialize the controller by loading data from files
  Future<void> initialize() async {
    try {
      _isLoading = true;
      notifyListeners();

      // Get the signage directory
      final signageDir = await StorageService.signageDirectory;
      final dataDir = '$signageDir/data';

      // Load campaigns
      final campaignsFile = File('$dataDir/campaigns.json');
      if (await campaignsFile.exists()) {
        final campaignsJson = await campaignsFile.readAsString();
        final campaignsData = jsonDecode(campaignsJson) as List;
        _campaigns = campaignsData
            .map((item) => Campaign.fromJson(item))
            .toList();
      }

      // Load schedule items
      final scheduleFile = File('$dataDir/schedule.json');
      if (await scheduleFile.exists()) {
        final scheduleJson = await scheduleFile.readAsString();
        final scheduleData = jsonDecode(scheduleJson) as List;
        _scheduleItems = scheduleData
            .map((item) => ScheduleItem.fromJson(item))
            .toList();
      }

      // Load media items
      final mediaFile = File('$dataDir/media.json');
      if (await mediaFile.exists()) {
        final mediaJson = await mediaFile.readAsString();
        final mediaData = jsonDecode(mediaJson) as List;
        _mediaItems = mediaData
            .map((item) => Media.fromJson(item))
            .toList();
      }

      // Filter campaigns based on current date
      _filterCampaigns();

      // Load saved state from persistent storage
      await loadState();

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      _errorMessage = 'Failed to load campaign data: $e';
      notifyListeners();
    }
  }

  /// Filter campaigns based on current date
  void _filterCampaigns() {
    final now = DateTime.now();

    // Filter campaigns that are active and within date range
    _campaigns = _campaigns.where((campaign) {
      return campaign.status == 'active' &&
          now.isAfter(campaign.startDate) &&
          now.isBefore(campaign.endDate);
    }).toList();

    // Reset indices
    _currentCampaignIndex = 0;
    _currentScheduleIndex = 0;
  }

  /// Move to the next schedule item
  void nextScheduleItem() {
    if (_scheduleItems.isEmpty) {
      debugPrint('No schedule items available');
      return;
    }

    final oldIndex = _currentScheduleIndex;
    _currentScheduleIndex = (_currentScheduleIndex + 1) % _scheduleItems.length;

    debugPrint('Moving from schedule item $oldIndex to $_currentScheduleIndex');
    debugPrint('New schedule item: ${_scheduleItems[_currentScheduleIndex].name}');

    notifyListeners();

    // Save the state after changing the index
    saveState();
  }

  /// Reset to the first schedule item
  void resetToFirstScheduleItem() {
    if (_scheduleItems.isEmpty) {
      debugPrint('No schedule items available');
      return;
    }

    final oldIndex = _currentScheduleIndex;
    _currentScheduleIndex = 0;

    debugPrint('Resetting from schedule item $oldIndex to 0');
    debugPrint('First schedule item: ${_scheduleItems[_currentScheduleIndex].name}');

    notifyListeners();

    // Save the state after changing the index
    saveState();
  }

  /// Move to the next campaign
  void nextCampaign() {
    if (_campaigns.isEmpty) {
      debugPrint('No campaigns available');
      return;
    }

    final oldIndex = _currentCampaignIndex;
    _currentCampaignIndex = (_currentCampaignIndex + 1) % _campaigns.length;
    _currentScheduleIndex = 0;

    debugPrint('Moving from campaign $oldIndex to $_currentCampaignIndex');
    if (_campaigns.isNotEmpty) {
      debugPrint('New campaign: ${_campaigns[_currentCampaignIndex].name}');
    }

    notifyListeners();

    // Save the state after changing the indices
    saveState();
  }

  /// Get the file path for a media item
  Future<String?> getMediaFilePath(String mediaName) async {
    try {
      final signageDir = await StorageService.signageDirectory;
      final contentDir = '$signageDir/content';

      return '$contentDir/$mediaName';
    } catch (e) {
      _errorMessage = 'Failed to get media file path: $e';
      notifyListeners();
      return null;
    }
  }

  /// Get the schedule items for the current campaign
  List<ScheduleItem> getScheduleItemsForCurrentCampaign() {
    if (currentCampaign == null) return [];

    return _scheduleItems.where((item) =>
        item.campaignId == currentCampaign!.id
    ).toList();
  }

  /// Check if a file is an image based on its extension
  bool isImageFile(String fileName) {
    final extension = fileName.split('.').last.toLowerCase();
    return ['jpg', 'jpeg', 'png', 'bmp', 'gif', 'apng', 'svg', 'tiff']
        .contains(extension);
  }

  /// Check if a file is a video based on its extension
  bool isVideoFile(String fileName) {
    final extension = fileName.split('.').last.toLowerCase();
    return ['mp4', 'webm'].contains(extension);
  }

  /// Save the current state to persistent storage
  Future<void> saveState() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt('currentCampaignIndex', _currentCampaignIndex);
      await prefs.setInt('currentScheduleIndex', _currentScheduleIndex);
      debugPrint('Saved state: campaign=$_currentCampaignIndex, schedule=$_currentScheduleIndex');
    } catch (e) {
      debugPrint('Error saving state: $e');
    }
  }

  /// Load the saved state from persistent storage
  Future<void> loadState() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Get the saved indices with fallback to 0
      final savedCampaignIndex = prefs.getInt('currentCampaignIndex') ?? 0;
      final savedScheduleIndex = prefs.getInt('currentScheduleIndex') ?? 0;

      // Validate the indices against the current lists
      if (_campaigns.isNotEmpty && savedCampaignIndex < _campaigns.length) {
        _currentCampaignIndex = savedCampaignIndex;
      }

      if (_scheduleItems.isNotEmpty && savedScheduleIndex < _scheduleItems.length) {
        _currentScheduleIndex = savedScheduleIndex;
      }

      debugPrint('Loaded state: campaign=$_currentCampaignIndex, schedule=$_currentScheduleIndex');
    } catch (e) {
      debugPrint('Error loading state: $e');
    }
  }

  /// Set the current schedule index to a specific value
  void setScheduleIndex(int index) {
    if (_scheduleItems.isEmpty) {
      debugPrint('No schedule items available');
      return;
    }

    if (index < 0 || index >= _scheduleItems.length) {
      debugPrint('Invalid schedule index: $index (valid range: 0-${_scheduleItems.length - 1})');
      return;
    }

    final oldIndex = _currentScheduleIndex;
    _currentScheduleIndex = index;

    debugPrint('Setting schedule index from $oldIndex to $_currentScheduleIndex');
    debugPrint('Schedule item: ${_scheduleItems[_currentScheduleIndex].name}');

    notifyListeners();

    // Save the state after changing the index
    saveState();
  }
}
