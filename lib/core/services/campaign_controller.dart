import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:math';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:signage/core/models/campaign.dart';
import 'package:signage/core/models/media.dart';
import 'package:signage/core/models/schedule_item.dart';
import 'package:signage/core/storage/storage_service.dart';

/// Controller for managing campaign playback
class CampaignController with ChangeNotifier {
  List<Campaign> _campaigns = [];
  List<ScheduleItem> _scheduleItems = [];
  List<Media> _mediaItems = [];

  int _currentCampaignIndex = 0;
  int _currentScheduleIndex = 0;

  bool _isLoading = true;
  String _errorMessage = '';

  // No caching - always fetch fresh data

  /// Get the current loading state
  bool get isLoading => _isLoading;

  /// Get the current error message
  String get errorMessage => _errorMessage;

  /// Get the list of campaigns
  List<Campaign> get campaigns => _campaigns;

  /// Get the list of schedule items
  List<ScheduleItem> get scheduleItems => _scheduleItems;

  /// Get the list of media items
  List<Media> get mediaItems => _mediaItems;

  /// Get the current campaign (basic getter - trigger checking happens in nextCampaign)
  Campaign? get currentCampaign {
    if (_campaigns.isEmpty || _currentCampaignIndex >= _campaigns.length) {
      return null;
    }
    return _campaigns[_currentCampaignIndex];
  }

  /// Get the current schedule item
  ScheduleItem? get currentScheduleItem =>
      _scheduleItems.isNotEmpty && _currentScheduleIndex < _scheduleItems.length
          ? _scheduleItems[_currentScheduleIndex]
          : null;

  /// Get the current schedule index
  int get currentScheduleIndex => _currentScheduleIndex;

  /// Initialize the controller by loading data from files
  Future<void> initialize() async {
    try {
      _isLoading = true;
      notifyListeners();

      // Get the signage directory
      final signageDir = await StorageService.signageDirectory;
      final dataDir = '$signageDir/data';

      // Load campaigns
      final campaignsFile = File('$dataDir/campaigns.json');
      if (await campaignsFile.exists()) {
        final campaignsJson = await campaignsFile.readAsString();
        final campaignsData = jsonDecode(campaignsJson) as List;
        _campaigns = campaignsData
            .map((item) => Campaign.fromJson(item))
            .toList();
      }

      // Load schedule items
      final scheduleFile = File('$dataDir/schedule.json');
      if (await scheduleFile.exists()) {
        final scheduleJson = await scheduleFile.readAsString();
        final scheduleData = jsonDecode(scheduleJson) as List;
        _scheduleItems = scheduleData
            .map((item) => ScheduleItem.fromJson(item))
            .toList();
      }

      // Load media items
      final mediaFile = File('$dataDir/media.json');
      if (await mediaFile.exists()) {
        final mediaJson = await mediaFile.readAsString();
        final mediaData = jsonDecode(mediaJson) as List;
        _mediaItems = mediaData
            .map((item) => Media.fromJson(item))
            .toList();
      }

      // Filter campaigns based on current date
      _filterCampaigns();

      // Load saved state from persistent storage
      await loadState();

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      _errorMessage = 'Failed to load campaign data: $e';
      notifyListeners();
    }
  }

  /// Filter campaigns based on current date
  void _filterCampaigns() {
    final now = DateTime.now();

    // Filter campaigns that are active and within date range
    _campaigns = _campaigns.where((campaign) {
      return campaign.status == 'active' &&
          now.isAfter(campaign.startDate) &&
          now.isBefore(campaign.endDate);
    }).toList();

    // Reset indices
    _currentCampaignIndex = 0;
    _currentScheduleIndex = 0;
  }

  /// Move to the next schedule item
  void nextScheduleItem() {
    if (_scheduleItems.isEmpty) {
      debugPrint('No schedule items available');
      return;
    }

    final oldIndex = _currentScheduleIndex;
    _currentScheduleIndex = (_currentScheduleIndex + 1) % _scheduleItems.length;

    debugPrint('Moving from schedule item $oldIndex to $_currentScheduleIndex');
    debugPrint('New schedule item: ${_scheduleItems[_currentScheduleIndex].name}');

    notifyListeners();

    // Save the state after changing the index
    saveState();
  }

  /// Reset to the first schedule item
  void resetToFirstScheduleItem() {
    if (_scheduleItems.isEmpty) {
      debugPrint('No schedule items available');
      return;
    }

    final oldIndex = _currentScheduleIndex;
    _currentScheduleIndex = 0;

    debugPrint('Resetting from schedule item $oldIndex to 0');
    debugPrint('First schedule item: ${_scheduleItems[_currentScheduleIndex].name}');

    notifyListeners();

    // Save the state after changing the index
    saveState();
  }

  /// Move to the next campaign
  void nextCampaign() {
    if (_campaigns.isEmpty) {
      debugPrint('No campaigns available');
      return;
    }

    final oldIndex = _currentCampaignIndex;
    _currentCampaignIndex = (_currentCampaignIndex + 1) % _campaigns.length;
    _currentScheduleIndex = 0;

    debugPrint('Moving from campaign $oldIndex to $_currentCampaignIndex');
    if (_campaigns.isNotEmpty) {
      debugPrint('New campaign: ${_campaigns[_currentCampaignIndex].name}');
    }

    notifyListeners();

    // Save the state after changing the indices
    saveState();
  }

  /// Get the file path for a media item
  Future<String?> getMediaFilePath(String mediaName) async {
    try {
      final signageDir = await StorageService.signageDirectory;
      final contentDir = '$signageDir/content';

      return '$contentDir/$mediaName';
    } catch (e) {
      _errorMessage = 'Failed to get media file path: $e';
      notifyListeners();
      return null;
    }
  }

  /// Get the schedule items for the current campaign
  List<ScheduleItem> getScheduleItemsForCurrentCampaign() {
    if (currentCampaign == null) return [];

    return _scheduleItems.where((item) =>
        item.campaignId == currentCampaign!.id
    ).toList();
  }

  /// Check if a file is an image based on its extension
  bool isImageFile(String fileName) {
    final extension = fileName.split('.').last.toLowerCase();
    return ['jpg', 'jpeg', 'png', 'bmp', 'gif', 'apng', 'svg', 'tiff']
        .contains(extension);
  }

  /// Check if a file is a video based on its extension
  bool isVideoFile(String fileName) {
    final extension = fileName.split('.').last.toLowerCase();
    return ['mp4', 'webm'].contains(extension);
  }

  /// Save the current state to persistent storage
  Future<void> saveState() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt('currentCampaignIndex', _currentCampaignIndex);
      await prefs.setInt('currentScheduleIndex', _currentScheduleIndex);
      debugPrint('Saved state: campaign=$_currentCampaignIndex, schedule=$_currentScheduleIndex');
    } catch (e) {
      debugPrint('Error saving state: $e');
    }
  }

  /// Load the saved state from persistent storage
  Future<void> loadState() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Get the saved indices with fallback to 0
      final savedCampaignIndex = prefs.getInt('currentCampaignIndex') ?? 0;
      final savedScheduleIndex = prefs.getInt('currentScheduleIndex') ?? 0;

      // Validate the indices against the current lists
      if (_campaigns.isNotEmpty && savedCampaignIndex < _campaigns.length) {
        _currentCampaignIndex = savedCampaignIndex;
      }

      if (_scheduleItems.isNotEmpty && savedScheduleIndex < _scheduleItems.length) {
        _currentScheduleIndex = savedScheduleIndex;
      }

      debugPrint('Loaded state: campaign=$_currentCampaignIndex, schedule=$_currentScheduleIndex');
    } catch (e) {
      debugPrint('Error loading state: $e');
    }
  }

  /// Set the current schedule index to a specific value
  void setScheduleIndex(int index) {
    if (_scheduleItems.isEmpty) {
      debugPrint('No schedule items available');
      return;
    }

    if (index < 0 || index >= _scheduleItems.length) {
      debugPrint('Invalid schedule index: $index (valid range: 0-${_scheduleItems.length - 1})');
      return;
    }

    final oldIndex = _currentScheduleIndex;
    _currentScheduleIndex = index;

    debugPrint('Setting schedule index from $oldIndex to $_currentScheduleIndex');
    debugPrint('Schedule item: ${_scheduleItems[_currentScheduleIndex].name}');

    notifyListeners();

    // Save the state after changing the index
    saveState();
  }

  /// Move to the next campaign with trigger checking
  Future<void> nextCampaignWithTriggerCheck() async {
    if (_campaigns.isEmpty) {
      debugPrint('No campaigns available');
      return;
    }

    debugPrint('=== CAMPAIGN TRIGGER CHECK START ===');
    debugPrint('Current campaign index: $_currentCampaignIndex');
    debugPrint('Total campaigns: ${_campaigns.length}');

    // Try each campaign starting from the current one (for loop back) or next one
    for (int i = 0; i < _campaigns.length; i++) {
      final campaignIndex = (_currentCampaignIndex + i) % _campaigns.length;
      final campaign = _campaigns[campaignIndex];

      debugPrint('Checking campaign: ${campaign.name} (trigger_type: ${campaign.triggerType})');

      if (await _shouldDisplayCampaign(campaign)) {
        final oldIndex = _currentCampaignIndex;
        _currentCampaignIndex = campaignIndex;
        _currentScheduleIndex = 0;

        debugPrint('Campaign ${campaign.name} meets trigger conditions!');
        if (oldIndex != _currentCampaignIndex) {
          debugPrint('Moving from campaign $oldIndex to $_currentCampaignIndex');
          debugPrint('New campaign: ${campaign.name}');
        } else {
          debugPrint('Staying on current campaign: ${campaign.name}');
        }

        notifyListeners();
        saveState();
        debugPrint('=== CAMPAIGN TRIGGER CHECK END ===');
        return;
      } else {
        debugPrint('Campaign ${campaign.name} does not meet trigger conditions, skipping');
      }
    }

    // If no campaign meets trigger conditions, stay on current campaign
    debugPrint('No campaigns meet trigger conditions, staying on current campaign');
    _currentScheduleIndex = 0; // Reset to first schedule item
    notifyListeners();
    saveState();
    debugPrint('=== CAMPAIGN TRIGGER CHECK END ===');
  }

  /// Check if a campaign should be displayed based on its trigger type
  Future<bool> _shouldDisplayCampaign(Campaign campaign) async {
    final triggerType = campaign.triggerType ?? 1; // Default to 1 if null

    switch (triggerType) {
      case 1: // Default Loop
        debugPrint('Default loop campaign: ${campaign.name} - displaying');
        return true;

      case 2: // Geofencing
        return await _checkGeofencingTrigger(campaign);

      case 3: // Weather
        return await _checkWeatherTrigger(campaign);

      case 4: // Audience Proximity (not implemented yet)
        debugPrint('Audience Proximity trigger not implemented yet');
        return false;

      default:
        debugPrint('Unknown trigger type: $triggerType, defaulting to true');
        return true;
    }
  }

  /// Check geofencing trigger condition
  Future<bool> _checkGeofencingTrigger(Campaign campaign) async {
    try {
      // Validate required geofencing parameters
      if (campaign.latitude == null ||
          campaign.longitude == null ||
          campaign.radius == null) {
        debugPrint('Geofencing campaign missing required parameters: lat=${campaign.latitude}, lon=${campaign.longitude}, radius=${campaign.radius}');
        return false;
      }

      // Get device location
      final deviceLocation = await _getDeviceLocation();

      // Check if device is inside geofence
      final isInside = _isInsideGeofence(
        campaign.latitude!,
        campaign.longitude!,
        deviceLocation.latitude,
        deviceLocation.longitude,
        campaign.radius!,
      );

      debugPrint('Geofencing check: campaign=${campaign.name}, device=(${deviceLocation.latitude}, ${deviceLocation.longitude}), geofence=(${campaign.latitude}, ${campaign.longitude}), radius=${campaign.radius}, inside=$isInside');
      return isInside;
    } catch (e) {
      debugPrint('Error checking geofencing trigger: $e');
      return false;
    }
  }

  /// Check weather trigger condition
  Future<bool> _checkWeatherTrigger(Campaign campaign) async {
    try {
      // Validate required weather parameters
      if (campaign.minTemp == null ||
          campaign.maxTemp == null ||
          campaign.units == null) {
        debugPrint('Weather campaign missing required parameters: minTemp=${campaign.minTemp}, maxTemp=${campaign.maxTemp}, units=${campaign.units}');
        return false;
      }

      // Get device location for weather API
      final deviceLocation = await _getDeviceLocation();

      // Get weather data
      final weatherData = await _fetchWeatherData(deviceLocation.latitude, deviceLocation.longitude, campaign.units!);

      // Check temperature range
      final currentTemp = weatherData['main']?['temp']?.toDouble();
      if (currentTemp == null) {
        debugPrint('Temperature data not available in weather response');
        return false;
      }

      final isInRange = currentTemp >= campaign.minTemp! && currentTemp <= campaign.maxTemp!;
      debugPrint('Weather check: campaign=${campaign.name}, temp=$currentTemp, range=${campaign.minTemp}-${campaign.maxTemp}, inRange=$isInRange');
      return isInRange;
    } catch (e) {
      debugPrint('Error checking weather trigger (network/API issue): $e');
      return false; // Skip campaign if weather API fails
    }
  }

  /// Get device location using geolocator
  Future<Position> _getDeviceLocation() async {
    bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
    LocationPermission permission = await Geolocator.checkPermission();

    if (!serviceEnabled || permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
      if (permission == LocationPermission.denied) {
        throw Exception("Location permission denied");
      }
    }

    return await Geolocator.getCurrentPosition(
      locationSettings: const LocationSettings(
        accuracy: LocationAccuracy.high,
        distanceFilter: 10,
      ),
    );
  }

  /// Fetch weather data from OpenWeatherMap API
  Future<Map<String, dynamic>> _fetchWeatherData(double lat, double lon, String units) async {
    const apiKey = '29b1d3623a51675e8ed3c1d3fa13c0f0';
    final url = 'https://api.openweathermap.org/data/2.5/weather?lat=$lat&lon=$lon&appid=$apiKey&units=$units';

    debugPrint('Fetching weather data from: $url');
    final response = await http.get(Uri.parse(url));

    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      debugPrint('Weather API response: ${data['main']?['temp']}°${_getTemperatureUnit(units)}');
      return data;
    } else {
      throw Exception('Failed to fetch weather data: ${response.statusCode}');
    }
  }

  /// Get temperature unit symbol
  String _getTemperatureUnit(String units) {
    switch (units) {
      case 'metric': return 'C';
      case 'imperial': return 'F';
      case 'standard': return 'K';
      default: return '';
    }
  }

  /// Check if device location is inside geofence using Haversine formula
  bool _isInsideGeofence(double geofenceLat, double geofenceLon, double deviceLat, double deviceLon, double radiusMeters) {
    const earthRadius = 6371000; // meters
    final dLat = (deviceLat - geofenceLat) * (pi / 180);
    final dLon = (deviceLon - geofenceLon) * (pi / 180);

    final a = sin(dLat / 2) * sin(dLat / 2) +
        cos(geofenceLat * (pi / 180)) *
            cos(deviceLat * (pi / 180)) *
            sin(dLon / 2) *
            sin(dLon / 2);

    final c = 2 * atan2(sqrt(a), sqrt(1 - a));
    final distance = earthRadius * c;

    return distance <= radiusMeters;
  }
}
