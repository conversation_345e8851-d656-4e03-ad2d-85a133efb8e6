import 'package:flutter/material.dart';
import 'package:signage/core/models/schedule_item.dart';
import 'package:signage/core/models/settings.dart';
import 'package:signage/core/services/campaign_controller.dart';
import 'package:signage/core/services/logging_service.dart';
import 'package:signage/core/services/player_controller_service.dart';
import 'package:signage/core/services/player_schedule_service.dart';
import 'package:signage/core/services/cursor_manager.dart';
import 'package:signage/ui/screens/data_loading_screen.dart';
import 'package:signage/ui/widgets/simple_media_widget.dart';
import 'package:signage/ui/widgets/slide_show_widget.dart';
import 'package:signage/ui/widgets/cursor_controlled_widget.dart';
import 'package:signage/utils/fullscreen_utils.dart';

/// The main player screen that displays content
class PlayerScreen extends StatefulWidget {
  const PlayerScreen({super.key});

  @override
  State<PlayerScreen> createState() => _PlayerScreenState();
}

class _PlayerScreenState extends State<PlayerScreen> {
  // Screen information
  String _screenName = '';
  String _screenCode = '';

  // Loading state
  bool _isLoading = true;
  String _errorMessage = '';

  // Content widgets
  Widget? _currentContentWidget;
  Widget? _nextContentWidget;

  // Transition state
  bool _isTransitioning = false;
  double _currentOpacity = 1.0;
  double _nextOpacity = 0.0;

  // Map to store content widgets by schedule item ID
  final Map<String, Widget> _contentWidgets = {};

  // Map to store GlobalKeys for each widget to access their state
  final Map<String, GlobalKey> _widgetKeys = {};

  // Currently active schedule item ID
  String? _activeScheduleItemId;
  String? _nextScheduleItemId;

  // Campaign controller
  late CampaignController _campaignController;

  // Player controller service for background updates
  final PlayerControllerService _playerControllerService = PlayerControllerService();

  // Logging service for activity and proof of play logging
  final LoggingService _loggingService = LoggingService();

  // Player schedule service for on/off timing
  late PlayerScheduleService _playerScheduleService;

  // Current schedule item
  ScheduleItem? _currentScheduleItem;

  // Settings for schedule timing
  Settings? _settings;

  // Whether content should be displayed (based on schedule)
  bool _shouldDisplayContent = true;

  @override
  void initState() {
    super.initState();
    FullscreenUtils.ensureFullscreen();
    _campaignController = CampaignController();

    // Initialize cursor manager for desktop platforms
    CursorManager.instance.initialize();

    // Initialize the player schedule service
    _playerScheduleService = PlayerScheduleService(
      onStartContent: _handleStartContent,
      onStopContent: _handleStopContent,
    );

    _initialize();

    // Start the player controller service for background updates
    _startPlayerControllerService();

    // Start the logging service
    _startLoggingService();
  }

  /// Start the logging service
  Future<void> _startLoggingService() async {
    debugPrint('Starting logging service');
    await _loggingService.start();

  }

  /// Start the player controller service
  void _startPlayerControllerService() {
    debugPrint('Starting player controller service');
    _playerControllerService.start(
      onPlayerRestartNeeded: _handlePlayerRestart,
    );
  }

  /// Handle player restart when data is refreshed
  void _handlePlayerRestart() {
    debugPrint('Player restart requested due to data update');

    if (!mounted) return;

    // Navigate to the data loading screen to reload data
    Navigator.of(context).pushReplacement(
      MaterialPageRoute(
        builder: (context) => const DataLoadingScreen(),
      ),
    );
  }

  @override
  void dispose() {
    debugPrint('Disposing PlayerScreen');

    // Dispose cursor manager
    CursorManager.instance.dispose();

    // Stop the player schedule service
    debugPrint('Stopping player schedule service');
    _playerScheduleService.stop();

    // Stop the player controller service
    debugPrint('Stopping player controller service');
    _playerControllerService.stop();

    // Stop the logging service
    debugPrint('Stopping logging service');
    _loggingService.stop();

    // Dispose all content widgets
    _disposeAllContentWidgets();

    // Clear collections
    _contentWidgets.clear();
    _widgetKeys.clear();

    // Reset active schedule item IDs
    _activeScheduleItemId = null;
    _nextScheduleItemId = null;

    // Reset transition state
    _isTransitioning = false;

    // Reset content widgets
    _currentContentWidget = null;
    _nextContentWidget = null;

    // Reset schedule item
    _currentScheduleItem = null;

    // Dispose the campaign controller
    _campaignController.dispose();

    super.dispose();
  }

  /// Dispose all content widgets
  void _disposeAllContentWidgets() {
    debugPrint('Disposing all content widgets: ${_contentWidgets.length} widgets');

    // We don't need to manually call dispose on the widgets as Flutter will handle that
    // Just clear the collections
    _contentWidgets.clear();
  }

  /// Dispose a specific content widget by schedule item ID
  void _disposeContentWidget(String scheduleItemId) {
    if (_contentWidgets.containsKey(scheduleItemId)) {
      debugPrint('====================================================');
      debugPrint('Disposing content widget for schedule item: $scheduleItemId');
      debugPrint('Current active schedule item ID: $_activeScheduleItemId');
      debugPrint('Current schedule item ID: ${_currentScheduleItem?.id}');
      debugPrint('Content widgets in memory: ${_contentWidgets.keys.join(', ')}');

      // Remove the widget from the map
      _contentWidgets.remove(scheduleItemId);

      // Remove the key from the map
      _widgetKeys.remove(scheduleItemId);

      debugPrint('Content widget disposed for schedule item: $scheduleItemId');
      debugPrint('Remaining widgets in memory: ${_contentWidgets.keys.join(', ')}');
      debugPrint('====================================================');
    } else {
      debugPrint('====================================================');
      debugPrint('Cannot dispose content widget for schedule item: $scheduleItemId - not found in widget map');
      debugPrint('Current widgets in memory: ${_contentWidgets.keys.join(', ')}');
      debugPrint('====================================================');
    }
  }

  /// Store a content widget by schedule item ID
  void _storeContentWidget(String scheduleItemId, Widget widget) {
    debugPrint('Storing content widget for schedule item: $scheduleItemId');

    // Store the widget in the map
    _contentWidgets[scheduleItemId] = widget;

    // Update the active schedule item ID
    _activeScheduleItemId = scheduleItemId;
  }

  /// Start the transition animation between current and next content
  void _startTransitionAnimation() {
    debugPrint('Starting transition animation');

    // Reset opacity values
    _currentOpacity = 1.0;
    _nextOpacity = 0.0;

    // Use an animation to fade between widgets
    Future.delayed(const Duration(milliseconds: 30), () {
      if (!mounted) return;

      // Start fading in the next content
      setState(() {
        _nextOpacity = 0.3;
      });

      Future.delayed(const Duration(milliseconds: 30), () {
        if (!mounted) return;

        setState(() {
          _nextOpacity = 0.6;
          _currentOpacity = 0.7;
        });

        Future.delayed(const Duration(milliseconds: 30), () {
          if (!mounted) return;

          setState(() {
            _nextOpacity = 1.0;
            _currentOpacity = 0.3;
          });

          // Complete the transition after a short delay
          Future.delayed(const Duration(milliseconds: 30), () async {
            if (!mounted) return;

            await _completeTransition();
          });
        });
      });
    });
  }

  /// Complete the transition by making the next content the current content
  Future<void> _completeTransition() async {
    debugPrint('====================================================');
    debugPrint('Completing transition');
    debugPrint('Current content ID: $_activeScheduleItemId');
    debugPrint('Next content ID: $_nextScheduleItemId');
    debugPrint('====================================================');

    if (!mounted) return;

    // Store the previous content ID for disposal
    final previousContentId = _activeScheduleItemId;

    // Check if we have a next content widget
    if (_nextContentWidget == null || _nextScheduleItemId == null) {
      debugPrint('WARNING: No next content widget or ID available for transition');

      // Check if we're at the last item in the schedule
      final isLastItem = _campaignController.currentScheduleIndex == _campaignController.scheduleItems.length - 1;

      if (isLastItem) {
        debugPrint('Last item in schedule detected, forcing loop back to first item');

        // Reset transition state
        setState(() {
          _isTransitioning = false;
          _currentOpacity = 1.0;
          _nextOpacity = 0.0;
        });

        // Explicitly set to the first schedule item
        _campaignController.resetToFirstScheduleItem();

        // Get the first schedule item
        final firstScheduleItem = _campaignController.currentScheduleItem;

        if (firstScheduleItem != null) {
          debugPrint('====================================================');
          debugPrint('Forcing playback of first schedule item: ${firstScheduleItem.name}');
          debugPrint('ID: ${firstScheduleItem.id}');
          debugPrint('====================================================');

          // Update the current schedule item
          _currentScheduleItem = firstScheduleItem;

          // Create the content widget for the first item
          _createContentWidget(firstScheduleItem);
        } else {
          debugPrint('ERROR: Failed to get first schedule item');
          // Move to the next campaign as a fallback
          await _campaignController.nextCampaignWithTriggerCheck();
          _startPlayback();
        }
        return;
      }

      // If we're at the first item but have no next content widget,
      // this might be due to a crash or skipping the first item
      if (_campaignController.currentScheduleIndex == 0) {
        debugPrint('First item in schedule, but no next content widget available');
        debugPrint('This might be due to a crash or skipping the first item');

        // Reset transition state
        setState(() {
          _isTransitioning = false;
          _currentOpacity = 1.0;
          _nextOpacity = 0.0;
        });

        // Explicitly set to the first schedule item to ensure we don't skip it
        _campaignController.setScheduleIndex(0);

        // Get the first schedule item
        final firstScheduleItem = _campaignController.currentScheduleItem;

        if (firstScheduleItem != null) {
          debugPrint('====================================================');
          debugPrint('Forcing playback of first schedule item: ${firstScheduleItem.name}');
          debugPrint('ID: ${firstScheduleItem.id}');
          debugPrint('====================================================');

          // Update the current schedule item
          _currentScheduleItem = firstScheduleItem;

          // Create the content widget for the first item
          _createContentWidget(firstScheduleItem);
        }
        return;
      }

      // For any other case, try to recover by moving to the next item
      debugPrint('Attempting to recover by moving to the next item');
      _campaignController.nextScheduleItem();
      final nextItem = _campaignController.currentScheduleItem;
      if (nextItem != null) {
        _currentScheduleItem = nextItem;
        _createContentWidget(nextItem);
      } else {
        // If all else fails, restart playback
        _startPlayback();
      }
      return;
    }

    // Update the current content with the next content
    setState(() {
      // Move next content to current
      _currentContentWidget = _nextContentWidget;
      _activeScheduleItemId = _nextScheduleItemId;

      // Reset next content
      _nextContentWidget = null;
      _nextScheduleItemId = null;

      // Reset opacity
      _currentOpacity = 1.0;
      _nextOpacity = 0.0;

      // End transition
      _isTransitioning = false;
    });

    // Dispose the previous content after a short delay
    // This ensures the new content is fully visible before disposing the old one
    if (previousContentId != null && previousContentId != _activeScheduleItemId) {
      Future.delayed(const Duration(milliseconds: 500), () {
        if (mounted) {
          debugPrint('Disposing previous content after transition: $previousContentId');
          _disposeContentWidget(previousContentId);
        }
      });
    }
  }

  /// Initialize the player
  Future<void> _initialize() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = '';
      });

      // Load settings
      await _loadSettings();

      // Initialize the campaign controller
      await _campaignController.initialize();

      // Start playback
      _startPlayback();

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      final errorMsg = 'Failed to initialize player: $e';

      // Log the error
      _loggingService.logError(errorMsg);

      setState(() {
        _isLoading = false;
        _errorMessage = errorMsg;
      });
    }
  }

  /// Load settings
  Future<void> _loadSettings() async {
    final settings = await Settings.load();
    if (settings != null && mounted) {
      _settings = settings;
      setState(() {
        _screenName = settings.screenName;
        _screenCode = settings.code;
      });

      // Initialize the player schedule service with settings
      await _playerScheduleService.initialize(settings);
      _playerScheduleService.start();
    }
  }

  /// Start media playback
  void _startPlayback() {
    // Check if content should be displayed based on schedule
    if (!_shouldDisplayContent) {
      debugPrint('PlayerScreen: Content should not be displayed according to schedule, skipping playback');
      return;
    }

    // Check for and clean up any orphaned widgets
    _cleanupOrphanedWidgets();

    // Check if we have campaigns and schedule items
    if (_campaignController.campaigns.isEmpty) {
      setState(() {
        _errorMessage = 'No active campaigns found';
      });
      return;
    }

    if (_campaignController.scheduleItems.isEmpty) {
      setState(() {
        _errorMessage = 'No schedule items found';
      });
      return;
    }

    // Get the current schedule item
    final scheduleItem = _campaignController.currentScheduleItem;
    if (scheduleItem == null) {
      setState(() {
        _errorMessage = 'No current schedule item';
      });
      return;
    }

    // Play the schedule item
    _playScheduleItem(scheduleItem);
  }

  /// Check for and clean up any orphaned widgets
  void _cleanupOrphanedWidgets() {
    // Get the current schedule items
    final currentScheduleItems = _campaignController.scheduleItems;

    // Create a set of valid schedule item IDs
    final validIds = currentScheduleItems.map((item) => item.id).toSet();

    // Find orphaned widgets (widgets for schedule items that no longer exist)
    final orphanedIds = _contentWidgets.keys.where((id) => !validIds.contains(id)).toList();

    if (orphanedIds.isNotEmpty) {
      debugPrint('====================================================');
      debugPrint('Found ${orphanedIds.length} orphaned widgets to clean up');

      // Dispose each orphaned widget
      for (final id in orphanedIds) {
        debugPrint('Cleaning up orphaned widget for schedule item: $id');
        _disposeContentWidget(id);
      }

      debugPrint('Orphaned widget cleanup complete');
      debugPrint('====================================================');
    }
  }

  /// Play a schedule item
  void _playScheduleItem(ScheduleItem scheduleItem) {
    debugPrint('Playing schedule item: ${scheduleItem.name} (type: ${scheduleItem.campaignType == 0 ? "SimpleMedia" : "Slide"})');

    // Store the current schedule item
    _currentScheduleItem = scheduleItem;

    // Create the content widget based on the schedule item type
    _createContentWidget(scheduleItem);
  }

  /// Create a content widget based on the schedule item type
  Future<void> _createContentWidget(ScheduleItem scheduleItem) async {
    // Output information about the current widget that has finished playing
    if (_currentContentWidget != null && _currentScheduleItem != null) {
      debugPrint('====================================================');
      debugPrint('Current widget that has finished playing:');
      debugPrint('Name: ${_currentScheduleItem!.name}');
      debugPrint('ID: ${_currentScheduleItem!.id}');
      debugPrint('Type: ${_currentScheduleItem!.campaignType == 0 ? "SimpleMedia" : "SlideShow"}');
      debugPrint('====================================================');
    } else {
      debugPrint('No current widget (first content or null widget)');
    }

    // Get the screen size
    final screenSize = MediaQuery.of(context).size;

    // Create the appropriate widget based on the schedule item type
    Widget contentWidget;

    // Create a GlobalKey for the widget
    final widgetKey = GlobalKey();
    _widgetKeys[scheduleItem.id] = widgetKey;

    if (scheduleItem.campaignType == 0) {
      // Simple media (image or video)
      final filePath = await _campaignController.getMediaFilePath(scheduleItem.name);
      if (filePath == null) {
        debugPrint('Failed to get file path for ${scheduleItem.name}');
        await _onContentComplete();
        return;
      }

      // Check if it's an image or video
      final isImage = _campaignController.isImageFile(scheduleItem.name);
      final isVideo = _campaignController.isVideoFile(scheduleItem.name);

      debugPrint('====================================================');
      debugPrint('Creating SimpleMediaWidget for ${scheduleItem.name}');
      debugPrint('ID: ${scheduleItem.id}');
      debugPrint('File Path: $filePath');
      debugPrint('Is Image: $isImage, Is Video: $isVideo');
      debugPrint('====================================================');

      contentWidget = SimpleMediaWidget(
        key: widgetKey,
        id: scheduleItem.id,
        name: scheduleItem.name,
        filePath: filePath,
        width: screenSize.width,
        height: screenSize.height,
        isImage: isImage,
        isVideo: isVideo,
        onComplete: _onContentComplete,
      );
    } else if (scheduleItem.campaignType == 1) {
      // Slide show
      debugPrint('====================================================');
      debugPrint('Creating SlideShowWidget for ${scheduleItem.name}');
      debugPrint('ID: ${scheduleItem.id}');
      debugPrint('====================================================');

      contentWidget = SlideShowWidget(
        key: widgetKey,
        scheduleItem: scheduleItem,
        campaignController: _campaignController,
        width: screenSize.width,
        height: screenSize.height,
        onComplete: _onContentComplete,
      );
    } else {
      // Unknown type, move to next item
      debugPrint('Unknown campaign type: ${scheduleItem.campaignType}');
      await _onContentComplete();
      return;
    }

    // Store the widget in our map
    _storeContentWidget(scheduleItem.id, contentWidget);

    // Log proof of play based on the content type
    final campaignId = _campaignController.currentCampaign?.id ?? '';

    if (scheduleItem.campaignType == 0) {
      // Simple media - log simple media display
      _loggingService.logSimpleMediaDisplay(campaignId, scheduleItem.id);
    } else if (scheduleItem.campaignType == 1) {
      // Slide show - log slide display
      _loggingService.logSlideDisplay(campaignId, scheduleItem.id);
    }

    // Check if we're in a transition
    if (_isTransitioning) {
      // This is the next widget in a transition
      if (mounted) {
        setState(() {
          _nextContentWidget = contentWidget;
          _nextScheduleItemId = scheduleItem.id;

          // Start the transition animation
          _startTransitionAnimation();
        });
      }
    } else {
      // First widget or direct replacement
      if (mounted) {
        setState(() {
          _currentContentWidget = contentWidget;
        });
      }
    }
  }

  /// Handle content completion
  Future<void> _onContentComplete() async {
    if (_currentScheduleItem == null) {
      return;
    }

    // Check if content should still be displayed based on schedule
    if (!_shouldDisplayContent) {
      debugPrint('PlayerScreen: Content should not be displayed according to schedule, stopping playback');
      // Clear current content and show black screen
      setState(() {
        _currentContentWidget = null;
      });
      return;
    }

    // Store the completed schedule item for later disposal
    final completedScheduleItem = _currentScheduleItem!;
    final completedScheduleItemId = completedScheduleItem.id;

    debugPrint('====================================================');
    debugPrint('Content completed, moving to next item');
    debugPrint('Completed widget details:');
    debugPrint('Name: ${completedScheduleItem.name}');
    debugPrint('ID: $completedScheduleItemId');
    debugPrint('Type: ${completedScheduleItem.campaignType == 0 ? "SimpleMedia" : "SlideShow"}');

    if (completedScheduleItem.campaignType == 0) {
      final isImage = _campaignController.isImageFile(completedScheduleItem.name);
      final isVideo = _campaignController.isVideoFile(completedScheduleItem.name);
      debugPrint('Media Type: ${isImage ? "Image" : isVideo ? "Video" : "Unknown"}');
    }
    debugPrint('====================================================');

    // Check if we're at the last schedule item
    final isLastItem = _campaignController.currentScheduleIndex == _campaignController.scheduleItems.length - 1;

    // Special handling for the last item in the schedule
    if (isLastItem) {
      debugPrint('Last item in schedule, checking for campaign triggers before looping');

      // Check if we should switch to a different campaign based on triggers
      await _campaignController.nextCampaignWithTriggerCheck();

      // Get the first schedule item of the (potentially new) campaign
      final firstScheduleItem = _campaignController.currentScheduleItem;

      if (firstScheduleItem != null) {
        debugPrint('====================================================');
        debugPrint('Playing first schedule item: ${firstScheduleItem.name}');
        debugPrint('ID: ${firstScheduleItem.id}');
        debugPrint('(Looping back to first item)');
        debugPrint('====================================================');

        // Update the current schedule item to the first one
        _currentScheduleItem = firstScheduleItem;

        // Start transition mode
        _isTransitioning = true;

        debugPrint('====================================================');
        debugPrint('Starting transition to first content: ${firstScheduleItem.name}');
        debugPrint('Current content: $completedScheduleItemId');
        debugPrint('Next content: ${firstScheduleItem.id}');
        debugPrint('====================================================');

        // Create the first content widget
        _createContentWidget(firstScheduleItem).then((_) {
          // Add a safety timer to dispose the old widget if something goes wrong
          Future.delayed(const Duration(seconds: 5), () {
            if (mounted && _contentWidgets.containsKey(completedScheduleItemId)) {
              debugPrint('Safety timer: disposing old content widget: $completedScheduleItemId');
              _disposeContentWidget(completedScheduleItemId);
            }
          });
        });
      } else {
        debugPrint('ERROR: Failed to get first schedule item');
        // Move to the next campaign as a fallback
        await _campaignController.nextCampaignWithTriggerCheck();
        _startPlayback();
      }
    } else {
      // Normal case - not the last item
      // Move to the next schedule item
      _campaignController.nextScheduleItem();

      // Get the new schedule item
      final nextScheduleItem = _campaignController.currentScheduleItem;

      if (nextScheduleItem != null) {
        debugPrint('====================================================');
        debugPrint('Playing next schedule item: ${nextScheduleItem.name}');
        debugPrint('ID: ${nextScheduleItem.id}');
        debugPrint('====================================================');

        // Update the current schedule item to the next one
        _currentScheduleItem = nextScheduleItem;

        // Start transition mode
        _isTransitioning = true;

        debugPrint('====================================================');
        debugPrint('Starting transition to next content: ${nextScheduleItem.name}');
        debugPrint('Current content: $completedScheduleItemId');
        debugPrint('Next content: ${nextScheduleItem.id}');
        debugPrint('====================================================');

        // Create the next content widget
        _createContentWidget(nextScheduleItem).then((_) {
          // The transition animation is started in _createContentWidget
          // After the transition completes, the previous widget will be disposed
          // in _completeTransition

          // Add a safety timer to dispose the old widget if something goes wrong
          Future.delayed(const Duration(seconds: 5), () {
            if (mounted && _contentWidgets.containsKey(completedScheduleItemId)) {
              debugPrint('Safety timer: disposing old content widget: $completedScheduleItemId');
              _disposeContentWidget(completedScheduleItemId);
            }
          });
        });
      } else {
        debugPrint('No more schedule items, moving to next campaign');

        // Dispose the completed widget
        _disposeContentWidget(completedScheduleItemId);

        // Clear all widgets before moving to the next campaign
        debugPrint('Clearing all widgets before moving to the next campaign');
        _disposeAllContentWidgets();

        // Reset the current content widget
        setState(() {
          _currentContentWidget = null;
        });

        // Move to the next campaign
        await _campaignController.nextCampaignWithTriggerCheck();

        // Start playback again
        _startPlayback();
      }
    }
  }

  /// Handle start content callback from schedule service
  void _handleStartContent() {
    debugPrint('PlayerScheduleService: Start content callback received');

    if (!mounted) return;

    setState(() {
      _shouldDisplayContent = true;
    });

    // Start playback if we have content
    if (_campaignController.scheduleItems.isNotEmpty) {
      _startPlayback();
    }
  }

  /// Handle stop content callback from schedule service
  void _handleStopContent() {
    debugPrint('PlayerScheduleService: Stop content callback received');

    if (!mounted) return;

    setState(() {
      _shouldDisplayContent = false;
      // Clear current content to show black screen immediately
      _currentContentWidget = null;
      _nextContentWidget = null;
      _isTransitioning = false;
    });

    // Dispose all content widgets to free up resources
    _disposeAllContentWidgets();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: null, // Remove app bar
      extendBodyBehindAppBar: true, // Extend body behind app bar
      body: CursorControlledWidget(
        child: _buildBody(),
      ),
    );
  }

  /// Build the body of the screen
  Widget _buildBody() {
    if (_isLoading) {
      // Show a loading indicator with transparent background
      return Container(
        color: Colors.transparent,
        child: const Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    if (_errorMessage.isNotEmpty) {
      // Show an error message
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              color: Colors.red,
              size: 48,
            ),
            const SizedBox(height: 16),
            Text(
              'Error',
              style: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              _errorMessage,
              style: const TextStyle(
                fontSize: 16,
                color: Colors.white70,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: _initialize,
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    // Check if content should be displayed based on schedule
    if (!_shouldDisplayContent) {
      // Show black screen when content should not be displayed
      return Container(
        color: Colors.black,
        width: double.infinity,
        height: double.infinity,
      );
    }

    // Display content with transition support
    if (_currentContentWidget != null) {
      // Log which widget is being displayed
      if (_activeScheduleItemId != null) {
        debugPrint('Displaying content widget for schedule item: $_activeScheduleItemId');
      }

      if (_isTransitioning && _nextContentWidget != null) {
        // During transition, show both widgets with opacity
        return Stack(
          children: [
            // Current content with fading out opacity
            Opacity(
              opacity: _currentOpacity,
              child: _currentContentWidget!,
            ),

            // Next content with fading in opacity
            Opacity(
              opacity: _nextOpacity,
              child: _nextContentWidget!,
            ),
          ],
        );
      } else {
        // Normal display - just show current content
        return _currentContentWidget!;
      }
    }

    // Show a placeholder
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Logo
            Image.asset(
              'assets/images/logo.png',
              width: 200,
              height: 200,
            ),
          const SizedBox(height: 20),

          // Screen info
          Text(
            'Screen: $_screenName',
            style: const TextStyle(
              fontSize: 24,
              color: Colors.black,
            ),
          ),
          Text(
            'Code: $_screenCode',
            style: const TextStyle(
              fontSize: 24,
              color: Colors.black,
            ),
          ),

          const SizedBox(height: 40),

          // Status message
          const Text(
            'No content to play',
            style: TextStyle(
              fontSize: 16,
              color: Colors.red,
            ),
          ),
        ],
      ),
    );
  }




}
